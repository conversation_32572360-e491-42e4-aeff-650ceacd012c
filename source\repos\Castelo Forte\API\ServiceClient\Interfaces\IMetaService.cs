using ServiceClient.Interfaces.Generic;
using Shared.Entities.Client;
using Shared.ViewModels.Client;

namespace ServiceClient.Interfaces
{
    /// <summary>
    /// Interface para serviço de Meta no contexto Client (multi-tenant)
    /// Refatorada para conter apenas 6 métodos essenciais
    /// </summary>
    public interface IMetaService : IGenericClientService<MetaViewModel, Meta>
    {
        /// <summary>
        /// 1. GetById - Retrieve a specific goal by its ID
        /// </summary>
        /// <param name="id">ID da meta</param>
        /// <returns>Meta encontrada ou null se não existir</returns>
        Task<MetaViewModel?> GetByIdAsync(string id);

        /// <summary>
        /// 2. GetAll - Retrieve all goals with filtering capabilities
        /// </summary>
        /// <param name="filtros">Filtros para aplicar na busca</param>
        /// <returns>Resposta paginada com metas filtradas</returns>
        Task<MetaPaginatedResponseViewModel> GetAllAsync(MetaFilterViewModel? filtros = null);

        /// <summary>
        /// 3. CreateOrUpdate - Combined method to create new goals or update existing ones
        /// </summary>
        /// <param name="meta">Dados da meta (ID presente = update, ID ausente = create)</param>
        /// <returns>Meta criada ou atualizada</returns>
        Task<MetaViewModel?> CreateOrUpdateAsync(MetaViewModel meta);

        /// <summary>
        /// 4. Delete - Soft delete/deactivate a goal (set active flag to false)
        /// </summary>
        /// <param name="id">ID da meta</param>
        /// <returns>True se a operação foi bem-sucedida</returns>
        Task<bool> DeleteAsync(string id);

        /// <summary>
        /// 5. GetFilterOptions - Return available filter options for the GetAll method
        /// </summary>
        /// <returns>Opções disponíveis para filtros</returns>
        Task<MetaFilterOptionsViewModel> GetFilterOptionsAsync();

        /// <summary>
        /// 6. CompleteOrUpdate - Mark a goal as completed or update its progress/status
        /// </summary>
        /// <param name="id">ID da meta</param>
        /// <param name="novoProgresso">Novo valor de progresso (opcional)</param>
        /// <param name="marcarComoConcluida">Se deve marcar como concluída</param>
        /// <returns>Meta atualizada</returns>
        Task<MetaViewModel?> CompleteOrUpdateAsync(string id, decimal? novoProgresso = null, bool marcarComoConcluida = false);


    }
}
