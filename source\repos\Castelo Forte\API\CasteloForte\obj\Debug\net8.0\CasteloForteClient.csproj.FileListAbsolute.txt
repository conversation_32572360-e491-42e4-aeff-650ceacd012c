C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\CasteloForteClient.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\rpswa.dswa.cache.json
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\CasteloForteClient.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\CasteloForteClient.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\CasteloForteClient.AssemblyInfo.cs
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\CasteloForteClient.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\CasteloForteClient.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\CasteloForteClient.sourcelink.json
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\CasteloForteClient.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\CasteloForteClient.exe
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\CasteloForteClient.deps.json
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\CasteloForteClient.runtimeconfig.json
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\CasteloForteClient.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\CasteloForteClient.pdb
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\HealthChecks.MongoDb.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\AutoMapper.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\DnsClient.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\FFMpegCore.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Instances.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\MailKit.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Diagnostics.Abstractions.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Diagnostics.HealthChecks.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Hosting.Abstractions.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.ObjectPool.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\MimeKit.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\MongoDB.EntityFrameworkCore.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\NETCore.MailKit.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\BouncyCastle.Crypto.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\RestSharp.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\SharpCompress.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Snappier.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\System.IO.Pipelines.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\System.Text.Json.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\ZstdSharp.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\RepositoryAdmin.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\RepositoryClient.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\ServiceAdmin.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\ServiceClient.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Shared.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\RepositoryAdmin.pdb
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\RepositoryClient.pdb
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\ServiceAdmin.pdb
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\ServiceClient.pdb
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\bin\Debug\net8.0\Shared.pdb
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\rjimswa.dswa.cache.json
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\rjsmrazor.dswa.cache.json
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\rjsmcshtml.dswa.cache.json
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\scopedcss\bundle\CasteloForteClient.styles.css
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\staticwebassets.build.json.cache
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\staticwebassets.upToDateCheck.txt
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\CasteloF.04F165E3.Up2Date
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\CasteloForteClient.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\refint\CasteloForteClient.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\CasteloForteClient.pdb
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\CasteloForteClient.genruntimeconfig.cache
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\ref\CasteloForteClient.dll
C:\Users\<USER>\source\repos\CasteloLeaoFinance\Castelo Forte\CasteloForte\CasteloForte\obj\Debug\net8.0\CasteloForteClient.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\CasteloForteClient.exe
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\CasteloForteClient.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\AutoMapper.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\DnsClient.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.OpenApi.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\MongoDB.Bson.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\MongoDB.Driver.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\MongoDB.EntityFrameworkCore.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\System.IO.Pipelines.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\System.Text.Json.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\RepositoryAdmin.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\RepositoryClient.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\ServiceAdmin.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\ServiceClient.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Shared.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\HealthChecks.MongoDb.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\FFMpegCore.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Instances.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\MailKit.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Diagnostics.Abstractions.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Diagnostics.HealthChecks.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Hosting.Abstractions.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.ObjectPool.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\MimeKit.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\NETCore.MailKit.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\BouncyCastle.Crypto.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\RestSharp.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\SharpCompress.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Snappier.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\ZstdSharp.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\runtimes\browser\lib\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\RepositoryAdmin.pdb
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\RepositoryClient.pdb
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\ServiceAdmin.pdb
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\ServiceClient.pdb
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\Shared.pdb
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\CasteloForteClient.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\rpswa.dswa.cache.json
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\CasteloForteClient.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\CasteloForteClient.AssemblyInfoInputs.cache
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\CasteloForteClient.AssemblyInfo.cs
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\CasteloForteClient.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\CasteloForteClient.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\CasteloForteClient.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\CasteloForteClient.sourcelink.json
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\rjimswa.dswa.cache.json
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\rjsmrazor.dswa.cache.json
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\rjsmcshtml.dswa.cache.json
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\scopedcss\bundle\CasteloForteClient.styles.css
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\staticwebassets.build.json
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\staticwebassets.build.json.cache
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\staticwebassets.development.json
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\CasteloForteClient.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\refint\CasteloForteClient.dll
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\CasteloForteClient.pdb
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\appsettings.json
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\CasteloForteClient.staticwebassets.endpoints.json
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\CasteloForteClient.deps.json
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\CasteloForteClient.runtimeconfig.json
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\bin\Debug\net8.0\CasteloForteClient.pdb
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\staticwebassets.upToDateCheck.txt
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\CasteloF.04F165E3.Up2Date
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\CasteloForteClient.genruntimeconfig.cache
C:\Users\<USER>\source\repos\Castelo Forte\API\CasteloForte\obj\Debug\net8.0\ref\CasteloForteClient.dll
