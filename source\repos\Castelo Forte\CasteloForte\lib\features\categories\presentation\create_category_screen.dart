import 'package:flutter/material.dart';
import '../data/categories_service.dart';
import '../models/category_form_model.dart';
import '../models/category_model.dart';

class CreateCategoryScreen extends StatefulWidget {
  final CategoryModel? categoryToEdit;

  const CreateCategoryScreen({super.key, this.categoryToEdit});

  @override
  State<CreateCategoryScreen> createState() => _CreateCategoryScreenState();
}

class _CreateCategoryScreenState extends State<CreateCategoryScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  String _selectedType = 'DESPESA';
  Color _selectedColor = Colors.blue;
  IconData _selectedIcon = Icons.category;

  bool get _isEditing => widget.categoryToEdit != null;
  String get _screenTitle =>
      _isEditing ? 'Editar Categoria' : 'Criar Categoria';
  String get _buttonText =>
      _isEditing ? 'Atualizar Categoria' : 'Criar Categoria';

  final List<String> _categoryTypes = ['DESPESA', 'RECEITA', 'META'];

  final List<Color> _availableColors = [
    Colors.blue,
    Colors.green,
    Colors.red,
    Colors.orange,
    Colors.purple,
    Colors.teal,
    Colors.pink,
    Colors.amber,
    Colors.indigo,
    Colors.cyan,
    Colors.lime,
    Colors.deepOrange,
  ];

  final List<IconData> _availableIcons = [
    Icons.category,
    Icons.shopping_cart,
    Icons.restaurant,
    Icons.local_gas_station,
    Icons.home,
    Icons.health_and_safety,
    Icons.school,
    Icons.sports_esports,
    Icons.movie,
    Icons.flight,
    Icons.directions_car,
    Icons.phone,
    Icons.wifi,
    Icons.fitness_center,
    Icons.pets,
    Icons.work,
    Icons.savings,
    Icons.account_balance,
    Icons.credit_card,
    Icons.monetization_on,
  ];

  @override
  void initState() {
    super.initState();
    _initializeFormData();
  }

  void _initializeFormData() {
    if (_isEditing && widget.categoryToEdit != null) {
      final category = widget.categoryToEdit!;
      _nameController.text = category.nome;
      _descriptionController.text = category.descricao;
      _selectedType = category.tipo;
      _selectedColor = category.colorValue;
      _selectedIcon = IconData(category.icone, fontFamily: 'MaterialIcons');
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          _isEditing ? 'Editar Categoria' : 'Nova Categoria',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header informativo
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF16213E),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: _selectedColor.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(Icons.add_circle, color: _selectedColor, size: 24),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Text(
                        'Crie uma nova categoria para organizar suas transações e metas',
                        style: TextStyle(color: Colors.white70),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),

              // Nome da categoria
              _buildTextField(
                controller: _nameController,
                label: 'Nome da Categoria',
                hint: 'Ex: Alimentação, Transporte...',
                icon: Icons.label,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Nome é obrigatório';
                  }
                  if (value.length < 2) {
                    return 'Nome deve ter pelo menos 2 caracteres';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),

              // Descrição
              _buildTextField(
                controller: _descriptionController,
                label: 'Descrição (opcional)',
                hint: 'Descreva o uso desta categoria...',
                icon: Icons.description,
                maxLines: 3,
              ),
              const SizedBox(height: 20),

              // Tipo de categoria
              _buildTypeSelector(),
              const SizedBox(height: 20),

              // Seletor de cor
              _buildColorSelector(),
              const SizedBox(height: 20),

              // Seletor de ícone
              _buildIconSelector(),
              const SizedBox(height: 30),

              // Preview da categoria
              _buildCategoryPreview(),
              const SizedBox(height: 30),

              // Botões de ação
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Colors.white54),
                        padding: const EdgeInsets.symmetric(vertical: 15),
                      ),
                      child: const Text(
                        'Cancelar',
                        style: TextStyle(color: Colors.white54),
                      ),
                    ),
                  ),
                  const SizedBox(width: 15),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _saveCategory,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _selectedColor,
                        padding: const EdgeInsets.symmetric(vertical: 15),
                      ),
                      child: Text(
                        _isEditing ? 'Atualizar Categoria' : 'Criar Categoria',
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          validator: validator,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.white54),
            prefixIcon: Icon(icon, color: Colors.white54),
            filled: true,
            fillColor: const Color(0xFF16213E),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: _selectedColor),
            ),
          ),
          onChanged: (value) => setState(() {}),
        ),
      ],
    );
  }

  Widget _buildTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Tipo de Categoria',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: _categoryTypes.map((type) {
            final isSelected = type == _selectedType;
            Color typeColor;
            IconData typeIcon;

            switch (type) {
              case 'RECEITA':
                typeColor = Colors.green;
                typeIcon = Icons.trending_up;
                break;
              case 'DESPESA':
                typeColor = Colors.red;
                typeIcon = Icons.trending_down;
                break;
              case 'META':
                typeColor = Colors.blue;
                typeIcon = Icons.flag;
                break;
              default:
                typeColor = Colors.grey;
                typeIcon = Icons.category;
            }

            return Expanded(
              child: Container(
                margin: EdgeInsets.only(
                  right: type != _categoryTypes.last ? 8 : 0,
                ),
                child: InkWell(
                  onTap: () => setState(() => _selectedType = type),
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isSelected ? typeColor : const Color(0xFF16213E),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected ? typeColor : Colors.white24,
                      ),
                    ),
                    child: Column(
                      children: [
                        Icon(
                          typeIcon,
                          color: isSelected ? Colors.white : typeColor,
                          size: 24,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          type,
                          style: TextStyle(
                            color: isSelected ? Colors.white : Colors.white70,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildColorSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Cor da Categoria',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children: _availableColors.map((color) {
            final isSelected = color == _selectedColor;
            return InkWell(
              onTap: () => setState(() => _selectedColor = color),
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected ? Colors.white : Colors.transparent,
                    width: 3,
                  ),
                ),
                child: isSelected
                    ? const Icon(Icons.check, color: Colors.white)
                    : null,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildIconSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Ícone da Categoria',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          height: 200,
          decoration: BoxDecoration(
            color: const Color(0xFF16213E),
            borderRadius: BorderRadius.circular(12),
          ),
          child: GridView.builder(
            padding: const EdgeInsets.all(12),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 6,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: _availableIcons.length,
            itemBuilder: (context, index) {
              final icon = _availableIcons[index];
              final isSelected = icon == _selectedIcon;

              return InkWell(
                onTap: () => setState(() => _selectedIcon = icon),
                child: Container(
                  decoration: BoxDecoration(
                    color: isSelected ? _selectedColor : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected ? _selectedColor : Colors.white24,
                    ),
                  ),
                  child: Icon(
                    icon,
                    color: isSelected ? Colors.white : Colors.white70,
                    size: 24,
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryPreview() {
    final categoryName = _nameController.text.isEmpty
        ? 'Nome da Categoria'
        : _nameController.text;
    final categoryDescription = _descriptionController.text.isEmpty
        ? 'Descrição da categoria'
        : _descriptionController.text;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: _selectedColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.visibility, color: _selectedColor, size: 24),
              const SizedBox(width: 12),
              const Text(
                'Preview da Categoria',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: _selectedColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Icon(_selectedIcon, color: _selectedColor, size: 30),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      categoryName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      categoryDescription,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _selectedColor.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _selectedType,
                        style: TextStyle(
                          color: _selectedColor,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _saveCategory() async {
    if (_formKey.currentState!.validate()) {
      try {
        // Criar modelo da categoria
        final category = CategoryFormModel(
          id: _isEditing ? widget.categoryToEdit!.id : null,
          nome: _nameController.text.trim(),
          descricao: _descriptionController.text.trim(),
          tipo: _selectedType,
          cor:
              (_selectedColor.a * 255).round() << 24 |
              (_selectedColor.r * 255).round() << 16 |
              (_selectedColor.g * 255).round() << 8 |
              (_selectedColor.b * 255).round(),
          icone: _selectedIcon.codePoint,
        );

        bool success;
        String successMessage;

        if (_isEditing) {
          // Atualizar categoria existente
          success = await CategoriesService.updateCategory(category);
          successMessage = success
              ? 'Categoria atualizada com sucesso!'
              : 'Erro ao atualizar categoria';
        } else {
          // Criar nova categoria
          success = await CategoriesService.createCategory(category);
          successMessage = success
              ? 'Categoria criada com sucesso!'
              : 'Erro ao criar categoria';
        }

        if (mounted) {
          Navigator.pop(context, success);

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(successMessage),
              backgroundColor: success ? Colors.green : Colors.red,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                _isEditing
                    ? 'Erro ao atualizar categoria. Tente novamente.'
                    : 'Erro ao criar categoria. Tente novamente.',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
