import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/widgets/app_header_internal.dart';
import '../../../core/widgets/app_footer.dart';
import '../data/categories_service.dart';
import '../models/category_model.dart';
import '../models/category_form_model.dart';

/// Tela para adicionar/editar categoria
class AddCategoryScreen extends StatefulWidget {
  final CategoryModel? category;

  const AddCategoryScreen({super.key, this.category});

  @override
  State<AddCategoryScreen> createState() => _AddCategoryScreenState();
}

class _AddCategoryScreenState extends State<AddCategoryScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nomeController = TextEditingController();
  final _descricaoController = TextEditingController();
  final _observacoesController = TextEditingController();
  final _limiteGastosController = TextEditingController();

  String _selectedTipo = 'DESPESA';
  String? _selectedPeriodoLimite;
  Color _selectedColor = const Color(0xFF4CAF50);
  IconData _selectedIcon = Icons.category;
  bool _isLoading = false;

  final List<Color> _availableColors = [
    const Color(0xFF4CAF50), // Verde
    const Color(0xFF2196F3), // Azul
    const Color(0xFFF44336), // Vermelho
    const Color(0xFFFF9800), // Laranja
    const Color(0xFF9C27B0), // Roxo
    const Color(0xFF607D8B), // Azul acinzentado
    const Color(0xFF795548), // Marrom
    const Color(0xFFE91E63), // Rosa
    const Color(0xFF00BCD4), // Ciano
    const Color(0xFFCDDC39), // Lima
    const Color(0xFFFF5722), // Vermelho profundo
    const Color(0xFF3F51B5), // Índigo
  ];

  final List<IconData> _availableIcons = [
    Icons.category,
    Icons.restaurant,
    Icons.directions_car,
    Icons.home,
    Icons.shopping_cart,
    Icons.local_gas_station,
    Icons.phone,
    Icons.lightbulb,
    Icons.medical_services,
    Icons.school,
    Icons.sports_esports,
    Icons.movie,
    Icons.fitness_center,
    Icons.pets,
    Icons.flight,
    Icons.hotel,
    Icons.work,
    Icons.attach_money,
    Icons.savings,
    Icons.account_balance,
  ];

  final List<String> _periodosLimite = ['DIARIO', 'SEMANAL', 'MENSAL', 'ANUAL'];

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  /// Inicializa o formulário com dados da categoria (se editando)
  void _initializeForm() {
    if (widget.category != null) {
      final category = widget.category!;
      _nomeController.text = category.nome;
      _descricaoController.text = category.descricao;
      _observacoesController.text = category.observacoes ?? '';
      _selectedTipo = category.tipo;
      _selectedColor = category.colorValue;
      _selectedIcon = category.iconData;
      _selectedPeriodoLimite = category.periodoLimite;

      if (category.limiteGastos != null) {
        _limiteGastosController.text = category.limiteGastos!.toStringAsFixed(
          2,
        );
      }
    }
  }

  /// Salva a categoria
  Future<void> _saveCategory() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final categoryForm = CategoryFormModel(
        id: widget.category?.id,
        nome: _nomeController.text.trim(),
        descricao: _descricaoController.text.trim(),
        tipo: _selectedTipo,
        cor:
            (_selectedColor.a * 255).round() << 24 |
            (_selectedColor.r * 255).round() << 16 |
            (_selectedColor.g * 255).round() << 8 |
            (_selectedColor.b * 255).round(),
        icone: _selectedIcon.codePoint,
        ordem: widget.category?.ordem ?? 0,
        observacoes: _observacoesController.text.trim().isEmpty
            ? null
            : _observacoesController.text.trim(),
        limiteGastos: _limiteGastosController.text.trim().isEmpty
            ? null
            : double.tryParse(
                _limiteGastosController.text.trim().replaceAll(',', '.'),
              ),
        periodoLimite: _selectedPeriodoLimite,
      );

      final success = widget.category == null
          ? await CategoriesService.createCategory(categoryForm)
          : await CategoriesService.updateCategory(categoryForm);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.category == null
                    ? 'Categoria criada com sucesso!'
                    : 'Categoria atualizada com sucesso!',
              ),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.pop(context, true);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Erro ao salvar categoria'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro inesperado: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.navyBlueColor,
      appBar: AppHeaderInternal(
        title: widget.category == null ? 'Nova Categoria' : 'Editar Categoria',
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Nome
              _buildTextField(
                controller: _nomeController,
                label: 'Nome da Categoria',
                hint: 'Ex: Alimentação, Transporte...',
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Nome é obrigatório';
                  }
                  if (value.trim().length < 2) {
                    return 'Nome deve ter pelo menos 2 caracteres';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Descrição
              _buildTextField(
                controller: _descricaoController,
                label: 'Descrição',
                hint: 'Descreva o que inclui nesta categoria...',
                maxLines: 3,
              ),

              const SizedBox(height: 16),

              // Tipo
              _buildDropdownField(
                label: 'Tipo',
                value: _selectedTipo,
                items: const [
                  DropdownMenuItem(value: 'RECEITA', child: Text('Receita')),
                  DropdownMenuItem(value: 'DESPESA', child: Text('Despesa')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedTipo = value!;
                  });
                },
              ),

              const SizedBox(height: 16),

              // Cor
              _buildColorSelector(),

              const SizedBox(height: 16),

              // Ícone
              _buildIconSelector(),

              const SizedBox(height: 16),

              // Limite de gastos (apenas para despesas)
              if (_selectedTipo == 'DESPESA') ...[
                _buildTextField(
                  controller: _limiteGastosController,
                  label: 'Limite de Gastos (Opcional)',
                  hint: '0,00',
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value != null && value.trim().isNotEmpty) {
                      final amount = double.tryParse(
                        value.trim().replaceAll(',', '.'),
                      );
                      if (amount == null || amount < 0) {
                        return 'Valor inválido';
                      }
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 16),

                // Período do limite
                if (_limiteGastosController.text.trim().isNotEmpty)
                  _buildDropdownField(
                    label: 'Período do Limite',
                    value: _selectedPeriodoLimite,
                    items: _periodosLimite.map((periodo) {
                      return DropdownMenuItem(
                        value: periodo,
                        child: Text(_formatPeriodo(periodo)),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedPeriodoLimite = value;
                      });
                    },
                  ),

                const SizedBox(height: 16),
              ],

              // Observações
              _buildTextField(
                controller: _observacoesController,
                label: 'Observações (Opcional)',
                hint: 'Informações adicionais...',
                maxLines: 3,
              ),

              const SizedBox(height: 32),

              // Botão salvar
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _saveCategory,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.goldColor,
                    foregroundColor: AppTheme.navyBlueColor,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                      : Text(
                          widget.category == null
                              ? 'Criar Categoria'
                              : 'Salvar Alterações',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: const AppFooter(currentIndex: -1),
    );
  }

  /// Constrói um campo de texto
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          keyboardType: keyboardType,
          validator: validator,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.white54),
            filled: true,
            fillColor: const Color(0xFF16213E),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppTheme.goldColor),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Colors.red),
            ),
          ),
        ),
      ],
    );
  }

  /// Constrói um campo dropdown
  Widget _buildDropdownField({
    required String label,
    required String? value,
    required List<DropdownMenuItem<String>> items,
    required void Function(String?) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: value,
          onChanged: onChanged,
          style: const TextStyle(color: Colors.white),
          dropdownColor: const Color(0xFF16213E),
          decoration: InputDecoration(
            filled: true,
            fillColor: const Color(0xFF16213E),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: AppTheme.goldColor),
            ),
          ),
          items: items,
        ),
      ],
    );
  }

  /// Constrói o seletor de cores
  Widget _buildColorSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Cor',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF16213E),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Wrap(
            spacing: 12,
            runSpacing: 12,
            children: _availableColors.map((color) {
              final isSelected = color.toARGB32() == _selectedColor.toARGB32();
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedColor = color;
                  });
                },
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                    border: isSelected
                        ? Border.all(color: Colors.white, width: 3)
                        : null,
                  ),
                  child: isSelected
                      ? const Icon(Icons.check, color: Colors.white, size: 20)
                      : null,
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  /// Constrói o seletor de ícones
  Widget _buildIconSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Ícone',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFF16213E),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Wrap(
            spacing: 12,
            runSpacing: 12,
            children: _availableIcons.map((icon) {
              final isSelected = icon.codePoint == _selectedIcon.codePoint;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedIcon = icon;
                  });
                },
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? _selectedColor.withValues(alpha: 0.3)
                        : Colors.white10,
                    borderRadius: BorderRadius.circular(8),
                    border: isSelected
                        ? Border.all(color: _selectedColor, width: 2)
                        : null,
                  ),
                  child: Icon(
                    icon,
                    color: isSelected ? _selectedColor : Colors.white54,
                    size: 20,
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  /// Formata o período do limite
  String _formatPeriodo(String periodo) {
    switch (periodo) {
      case 'DIARIO':
        return 'Diário';
      case 'SEMANAL':
        return 'Semanal';
      case 'MENSAL':
        return 'Mensal';
      case 'ANUAL':
        return 'Anual';
      default:
        return periodo;
    }
  }

  @override
  void dispose() {
    _nomeController.dispose();
    _descricaoController.dispose();
    _observacoesController.dispose();
    _limiteGastosController.dispose();
    super.dispose();
  }
}
