import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../data/goals_service.dart';
import '../models/goal_model.dart';
import '../models/meta_request_models.dart';
import '../../../core/services/logger_service.dart';

class CreateGoalScreen extends StatefulWidget {
  final Map<String, dynamic>? existingGoal;
  final bool isEditing;

  const CreateGoalScreen({
    super.key,
    this.existingGoal,
    this.isEditing = false,
  });

  @override
  State<CreateGoalScreen> createState() => _CreateGoalScreenState();
}

class _CreateGoalScreenState extends State<CreateGoalScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _targetAmountController = TextEditingController();
  final _currentAmountController = TextEditingController();

  DateTime _targetDate = DateTime.now().add(const Duration(days: 365));
  String _selectedIcon = '🎯';
  Color _selectedColor = const Color(0xFF4CAF50);
  bool _isMonthlyGoal = false;
  List<Map<String, dynamic>> _selectedCategories = [];

  // Lista de categorias disponíveis (será carregada do backend)
  final List<Map<String, dynamic>> _availableCategories = [
    {
      'id': '1',
      'nome': 'Casa Própria',
      'tipo': 'META',
      'cor': Colors.orange.toARGB32(),
      'icone': Icons.home.codePoint,
    },
    {
      'id': '2',
      'nome': 'Viagem',
      'tipo': 'META',
      'cor': Colors.blue.toARGB32(),
      'icone': Icons.flight.codePoint,
    },
    {
      'id': '3',
      'nome': 'Educação',
      'tipo': 'META',
      'cor': Colors.purple.toARGB32(),
      'icone': Icons.school.codePoint,
    },
    {
      'id': '4',
      'nome': 'Emergência',
      'tipo': 'META',
      'cor': Colors.red.toARGB32(),
      'icone': Icons.warning.codePoint,
    },
    {
      'id': '5',
      'nome': 'Aposentadoria',
      'tipo': 'META',
      'cor': Colors.green.toARGB32(),
      'icone': Icons.elderly.codePoint,
    },
  ];

  final List<String> _availableIcons = [
    '🎯',
    '💰',
    '🏠',
    '🚗',
    '✈️',
    '🎓',
    '💍',
    '🏖️',
    '📱',
    '💻',
    '🎮',
    '📚',
    '🎸',
    '🏋️',
    '🍕',
    '☕',
    '🛍️',
    '💊',
    '🐕',
    '🌱',
  ];

  final List<Color> _availableColors = [
    const Color(0xFF4CAF50),
    const Color(0xFF2196F3),
    const Color(0xFFFF9800),
    const Color(0xFFE91E63),
    const Color(0xFF9C27B0),
    const Color(0xFF00BCD4),
    const Color(0xFFFFEB3B),
    const Color(0xFFFF5722),
    const Color(0xFF795548),
    const Color(0xFF607D8B),
  ];

  @override
  void initState() {
    super.initState();
    if (widget.existingGoal != null) {
      _loadExistingGoal();
    }
  }

  void _loadExistingGoal() {
    final goal = widget.existingGoal!;
    _nameController.text = goal['nome'] ?? '';
    _descriptionController.text = goal['descricao'] ?? '';
    _targetAmountController.text = (goal['valorAlvo'] ?? 0.0).toString();
    _currentAmountController.text = (goal['valorAtual'] ?? 0.0).toString();

    // Parse dates from ISO string format
    if (goal['dataConclusao'] != null) {
      try {
        _targetDate = DateTime.parse(goal['dataConclusao']);
      } catch (e) {
        _targetDate = DateTime.now().add(const Duration(days: 365));
      }
    } else {
      _targetDate = DateTime.now().add(const Duration(days: 365));
    }

    _selectedIcon = '🎯'; // Default icon since MetaViewModel doesn't have icon
    _selectedColor = const Color(0xFF4CAF50); // Default color
    _isMonthlyGoal = goal['flgMensal'] ?? false;

    // Load selected categories if available
    if (goal['categorias'] != null) {
      final categorias = goal['categorias'] as List<dynamic>?;
      if (categorias != null && categorias.isNotEmpty) {
        _selectedCategories = _availableCategories
            .where(
              (cat) => categorias.any((goalCat) => goalCat['id'] == cat['id']),
            )
            .toList();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.existingGoal != null;

    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          isEditing ? 'Editar Meta' : 'Nova Meta',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _saveGoal,
            child: Text(
              isEditing ? 'Salvar' : 'Criar',
              style: const TextStyle(
                color: Color(0xFF4ECDC4),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header informativo
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF16213E),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: const Color(0xFF4ECDC4).withValues(alpha: 0.3),
                  ),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.add_circle, color: Color(0xFF4ECDC4), size: 24),
                    SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Crie uma nova meta para alcançar seus objetivos financeiros',
                        style: TextStyle(color: Colors.white70),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),

              // Preview da meta
              _buildGoalPreview(),
              const SizedBox(height: 30),

              // Nome da meta
              _buildTextField(
                controller: _nameController,
                label: 'Nome da Meta',
                hint: 'Ex: Comprar casa própria',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Nome é obrigatório';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),

              // Descrição
              _buildTextField(
                controller: _descriptionController,
                label: 'Descrição (opcional)',
                hint: 'Descreva sua meta...',
                maxLines: 3,
              ),
              const SizedBox(height: 20),

              // Valor alvo
              _buildTextField(
                controller: _targetAmountController,
                label: 'Valor Alvo',
                hint: '0,00',
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Valor alvo é obrigatório';
                  }
                  final amount = double.tryParse(value.replaceAll(',', '.'));
                  if (amount == null || amount <= 0) {
                    return 'Valor deve ser maior que zero';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),

              // Valor atual
              _buildTextField(
                controller: _currentAmountController,
                label: 'Valor Atual',
                hint: '0,00',
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
                ],
              ),
              const SizedBox(height: 20),

              // Data alvo
              _buildDateField(),
              const SizedBox(height: 20),

              // Meta mensal
              _buildMonthlyToggle(),
              const SizedBox(height: 30),

              // Seleção de ícone
              _buildIconSelection(),
              const SizedBox(height: 30),

              // Seleção de cor
              _buildColorSelection(),
              const SizedBox(height: 30),

              // Categorias associadas
              _buildCategorySelection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGoalPreview() {
    final targetAmount =
        double.tryParse(_targetAmountController.text.replaceAll(',', '.')) ??
        0.0;
    final currentAmount =
        double.tryParse(_currentAmountController.text.replaceAll(',', '.')) ??
        0.0;
    final progress = targetAmount > 0
        ? (currentAmount / targetAmount).clamp(0.0, 1.0)
        : 0.0;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: _selectedColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: _selectedColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: _selectedColor,
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Center(
                  child: Text(
                    _selectedIcon,
                    style: const TextStyle(fontSize: 24),
                  ),
                ),
              ),
              const SizedBox(width: 15),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _nameController.text.isEmpty
                          ? 'Nome da Meta'
                          : _nameController.text,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'R\$ ${currentAmount.toStringAsFixed(2)} / R\$ ${targetAmount.toStringAsFixed(2)}',
                      style: const TextStyle(color: Colors.white70),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          LinearProgressIndicator(
            value: progress,
            backgroundColor: Colors.white24,
            valueColor: AlwaysStoppedAnimation<Color>(_selectedColor),
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${(progress * 100).toStringAsFixed(1)}% concluído',
                style: const TextStyle(color: Colors.white70),
              ),
              Text(
                _formatDate(_targetDate),
                style: const TextStyle(color: Colors.white70),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          validator: validator,
          maxLines: maxLines,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.white54),
            filled: true,
            fillColor: const Color(0xFF16213E),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF4CAF50)),
            ),
          ),
          onChanged: (value) => setState(() {}),
        ),
      ],
    );
  }

  Widget _buildDateField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Data Alvo',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: _selectDate,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF16213E),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(Icons.calendar_today, color: Colors.white54),
                const SizedBox(width: 12),
                Text(
                  _formatDate(_targetDate),
                  style: const TextStyle(color: Colors.white),
                ),
                const Spacer(),
                const Icon(Icons.arrow_drop_down, color: Colors.white54),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMonthlyToggle() {
    return Row(
      children: [
        Switch(
          value: _isMonthlyGoal,
          onChanged: (value) => setState(() => _isMonthlyGoal = value),
          activeColor: const Color(0xFF4CAF50),
        ),
        const SizedBox(width: 12),
        const Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Meta Mensal',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                'A meta será renovada mensalmente',
                style: TextStyle(color: Colors.white70, fontSize: 12),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildIconSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Ícone da Meta',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 10,
          runSpacing: 10,
          children: _availableIcons.map((icon) {
            final isSelected = icon == _selectedIcon;
            return InkWell(
              onTap: () => setState(() => _selectedIcon = icon),
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: isSelected ? _selectedColor : Colors.white10,
                  borderRadius: BorderRadius.circular(25),
                  border: Border.all(
                    color: isSelected ? _selectedColor : Colors.white24,
                  ),
                ),
                child: Center(
                  child: Text(icon, style: const TextStyle(fontSize: 24)),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildColorSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Cor da Meta',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 10,
          runSpacing: 10,
          children: _availableColors.map((color) {
            final isSelected = color == _selectedColor;
            return InkWell(
              onTap: () => setState(() => _selectedColor = color),
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected ? Colors.white : Colors.transparent,
                    width: 3,
                  ),
                ),
                child: isSelected
                    ? const Icon(Icons.check, color: Colors.white, size: 20)
                    : null,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildCategorySelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Categorias Associadas',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _availableCategories.map((category) {
            final isSelected = _selectedCategories.contains(category);
            return InkWell(
              onTap: () {
                setState(() {
                  if (isSelected) {
                    _selectedCategories.remove(category);
                  } else {
                    _selectedCategories.add(category);
                  }
                });
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: isSelected ? _selectedColor : Colors.white10,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected ? _selectedColor : Colors.white24,
                  ),
                ),
                child: Text(
                  category['nome'],
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.white70,
                    fontSize: 12,
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _targetDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 3650)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: Color(0xFF4CAF50),
              surface: Color(0xFF16213E),
            ),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      setState(() => _targetDate = date);
    }
  }

  void _saveGoal() async {
    if (_formKey.currentState!.validate()) {
      final goalData = {
        'name': _nameController.text,
        'description': _descriptionController.text,
        'targetAmount':
            double.tryParse(
              _targetAmountController.text.replaceAll(',', '.'),
            ) ??
            0.0,
        'currentAmount':
            double.tryParse(
              _currentAmountController.text.replaceAll(',', '.'),
            ) ??
            0.0,
        'targetDate': _targetDate,
        'icon': _selectedIcon,
        'color': _selectedColor.toARGB32(),
        'isMonthly': _isMonthlyGoal,
        'categories': _selectedCategories.map((cat) => cat['id']).toList(),
      };

      try {
        LoggerService.info('Salvando meta: ${goalData['name']}');
        LoggerService.info('Dados da meta: $goalData');

        // Criar modelo da requisição
        final request = MetaCreateUpdateRequestModel(
          id: widget.isEditing && widget.existingGoal != null
              ? widget.existingGoal!['id']?.toString()
              : null,
          nome: goalData['name']?.toString() ?? '',
          descricao: goalData['description']?.toString(),
          valorAlvo: (goalData['targetAmount'] as num?)?.toDouble() ?? 0.0,
          progressoAtual:
              (goalData['currentAmount'] as num?)?.toDouble() ?? 0.0,
          dataAbertura: DateTime.now(),
          dataConclusao:
              goalData['targetDate'] as DateTime? ??
              DateTime.now().add(const Duration(days: 365)),
          categoriasIds: _selectedCategories.isNotEmpty
              ? [_selectedCategories.first['id']?.toString() ?? '']
              : null,
          flgMensal: _isMonthlyGoal,
        );

        LoggerService.info('Request criado: ${request.toJson()}');

        // Salvar meta no backend
        final response = await GoalsService.createOrUpdateMeta(request);

        LoggerService.info('Meta salva com sucesso: ${response.nome}');

        if (mounted) {
          Navigator.pop(context, response.toJson());

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                widget.isEditing
                    ? 'Meta atualizada com sucesso!'
                    : 'Meta criada com sucesso!',
              ),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        LoggerService.failure('Erro ao salvar meta: $e');

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Erro ao ${widget.isEditing ? 'atualizar' : 'criar'} meta: $e',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
