/// Modelo para resposta paginada de metas
class MetaPaginatedResponseModel {
  final List<MetaViewModel> metas;
  final PaginacaoInfo paginacao;
  final Map<String, dynamic> filtrosAplicados;
  final EstatisticasResultado estatisticas;

  const MetaPaginatedResponseModel({
    required this.metas,
    required this.paginacao,
    required this.filtrosAplicados,
    required this.estatisticas,
  });

  factory MetaPaginatedResponseModel.fromJson(Map<String, dynamic> json) {
    return MetaPaginatedResponseModel(
      metas: (json['metas'] as List?)
          ?.map((meta) => MetaViewModel.fromJson(meta as Map<String, dynamic>))
          .toList() ?? [],
      paginacao: PaginacaoInfo.fromJson(json['paginacao'] as Map<String, dynamic>? ?? {}),
      filtrosAplicados: json['filtrosAplicados'] as Map<String, dynamic>? ?? {},
      estatisticas: EstatisticasResultado.fromJson(json['estatisticas'] as Map<String, dynamic>? ?? {}),
    );
  }
}

/// Modelo para informações de paginação
class PaginacaoInfo {
  final int paginaAtual;
  final int totalPaginas;
  final int totalRegistros;
  final int tamanhoPagina;
  final bool temProximaPagina;
  final bool temPaginaAnterior;

  const PaginacaoInfo({
    required this.paginaAtual,
    required this.totalPaginas,
    required this.totalRegistros,
    required this.tamanhoPagina,
    required this.temProximaPagina,
    required this.temPaginaAnterior,
  });

  factory PaginacaoInfo.fromJson(Map<String, dynamic> json) {
    return PaginacaoInfo(
      paginaAtual: json['paginaAtual'] as int? ?? 1,
      totalPaginas: json['totalPaginas'] as int? ?? 1,
      totalRegistros: json['totalRegistros'] as int? ?? 0,
      tamanhoPagina: json['tamanhoPagina'] as int? ?? 10,
      temProximaPagina: json['temProximaPagina'] as bool? ?? false,
      temPaginaAnterior: json['temPaginaAnterior'] as bool? ?? false,
    );
  }
}

/// Modelo para estatísticas de resultado
class EstatisticasResultado {
  final int totalEncontradas;
  final Map<String, int> quantidadePorStatus;
  final double valorTotalMetas;
  final double valorTotalAlcancado;
  final double progressoMedio;

  const EstatisticasResultado({
    required this.totalEncontradas,
    required this.quantidadePorStatus,
    required this.valorTotalMetas,
    required this.valorTotalAlcancado,
    required this.progressoMedio,
  });

  factory EstatisticasResultado.fromJson(Map<String, dynamic> json) {
    return EstatisticasResultado(
      totalEncontradas: json['totalEncontradas'] as int? ?? 0,
      quantidadePorStatus: Map<String, int>.from(json['quantidadePorStatus'] as Map? ?? {}),
      valorTotalMetas: (json['valorTotalMetas'] as num?)?.toDouble() ?? 0.0,
      valorTotalAlcancado: (json['valorTotalAlcancado'] as num?)?.toDouble() ?? 0.0,
      progressoMedio: (json['progressoMedio'] as num?)?.toDouble() ?? 0.0,
    );
  }
}

/// Modelo para ViewModel de Meta da API
class MetaViewModel {
  final String? id;
  final String nome;
  final String? descricao;
  final double valorAlvo;
  final double progressoAtual;
  final double percentualProgresso;
  final DateTime dtaCadastro;
  final DateTime? dataAbertura;
  final DateTime? dataConclusao;
  final String status;
  final String statusDescricao;
  final bool flgAtivo;
  final bool flgMensal;
  final List<String> categoriasAssociadas;
  final bool isVencida;

  const MetaViewModel({
    this.id,
    required this.nome,
    this.descricao,
    required this.valorAlvo,
    required this.progressoAtual,
    required this.percentualProgresso,
    required this.dtaCadastro,
    this.dataAbertura,
    this.dataConclusao,
    required this.status,
    required this.statusDescricao,
    required this.flgAtivo,
    required this.flgMensal,
    required this.categoriasAssociadas,
    required this.isVencida,
  });

  factory MetaViewModel.fromJson(Map<String, dynamic> json) {
    return MetaViewModel(
      id: json['id']?.toString(),
      nome: json['nome']?.toString() ?? '',
      descricao: json['descricao']?.toString(),
      valorAlvo: (json['valorAlvo'] as num?)?.toDouble() ?? 0.0,
      progressoAtual: (json['progressoAtual'] as num?)?.toDouble() ?? 0.0,
      percentualProgresso: (json['percentualProgresso'] as num?)?.toDouble() ?? 0.0,
      dtaCadastro: json['dtaCadastro'] != null 
          ? DateTime.parse(json['dtaCadastro'].toString())
          : DateTime.now(),
      dataAbertura: json['dataAbertura'] != null 
          ? DateTime.parse(json['dataAbertura'].toString())
          : null,
      dataConclusao: json['dataConclusao'] != null 
          ? DateTime.parse(json['dataConclusao'].toString())
          : null,
      status: json['status']?.toString() ?? '',
      statusDescricao: json['statusDescricao']?.toString() ?? '',
      flgAtivo: json['flgAtivo'] as bool? ?? true,
      flgMensal: json['flgMensal'] as bool? ?? false,
      categoriasAssociadas: List<String>.from(json['categoriasAssociadas'] as List? ?? []),
      isVencida: json['isVencida'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nome': nome,
      'descricao': descricao,
      'valorAlvo': valorAlvo,
      'progressoAtual': progressoAtual,
      'percentualProgresso': percentualProgresso,
      'dtaCadastro': dtaCadastro.toIso8601String(),
      'dataAbertura': dataAbertura?.toIso8601String(),
      'dataConclusao': dataConclusao?.toIso8601String(),
      'status': status,
      'statusDescricao': statusDescricao,
      'flgAtivo': flgAtivo,
      'flgMensal': flgMensal,
      'categoriasAssociadas': categoriasAssociadas,
      'isVencida': isVencida,
    };
  }

  /// Calcula o progresso da meta (0.0 a 1.0)
  double get progresso {
    if (valorAlvo <= 0) return 0.0;
    final progress = progressoAtual / valorAlvo;
    return progress > 1.0 ? 1.0 : progress;
  }

  /// Verifica se a meta foi concluída
  bool get isConcluida => progressoAtual >= valorAlvo;

  /// Calcula quantos dias restam para a meta
  int get diasRestantes {
    if (dataConclusao == null) return 0;
    final agora = DateTime.now();
    if (dataConclusao!.isBefore(agora)) return 0;
    return dataConclusao!.difference(agora).inDays;
  }

  /// Calcula quanto falta para atingir a meta
  double get valorRestante {
    final restante = valorAlvo - progressoAtual;
    return restante > 0 ? restante : 0.0;
  }

  /// Retorna o valor atual formatado
  String get valorAtualFormatado {
    return 'R\$ ${progressoAtual.toStringAsFixed(2).replaceAll('.', ',')}';
  }

  /// Retorna o valor da meta formatado
  String get valorMetaFormatado {
    return 'R\$ ${valorAlvo.toStringAsFixed(2).replaceAll('.', ',')}';
  }

  /// Retorna o valor restante formatado
  String get valorRestanteFormatado {
    return 'R\$ ${valorRestante.toStringAsFixed(2).replaceAll('.', ',')}';
  }

  /// Cria uma cópia com campos modificados
  MetaViewModel copyWith({
    String? id,
    String? nome,
    String? descricao,
    double? valorAlvo,
    double? progressoAtual,
    double? percentualProgresso,
    DateTime? dtaCadastro,
    DateTime? dataAbertura,
    DateTime? dataConclusao,
    String? status,
    String? statusDescricao,
    bool? flgAtivo,
    bool? flgMensal,
    List<String>? categoriasAssociadas,
    bool? isVencida,
  }) {
    return MetaViewModel(
      id: id ?? this.id,
      nome: nome ?? this.nome,
      descricao: descricao ?? this.descricao,
      valorAlvo: valorAlvo ?? this.valorAlvo,
      progressoAtual: progressoAtual ?? this.progressoAtual,
      percentualProgresso: percentualProgresso ?? this.percentualProgresso,
      dtaCadastro: dtaCadastro ?? this.dtaCadastro,
      dataAbertura: dataAbertura ?? this.dataAbertura,
      dataConclusao: dataConclusao ?? this.dataConclusao,
      status: status ?? this.status,
      statusDescricao: statusDescricao ?? this.statusDescricao,
      flgAtivo: flgAtivo ?? this.flgAtivo,
      flgMensal: flgMensal ?? this.flgMensal,
      categoriasAssociadas: categoriasAssociadas ?? this.categoriasAssociadas,
      isVencida: isVencida ?? this.isVencida,
    );
  }

  @override
  String toString() {
    return 'MetaViewModel(id: $id, nome: $nome, progresso: ${percentualProgresso.toStringAsFixed(1)}%)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MetaViewModel &&
        other.id == id &&
        other.nome == nome &&
        other.valorAlvo == valorAlvo &&
        other.progressoAtual == progressoAtual;
  }

  @override
  int get hashCode {
    return Object.hash(id, nome, valorAlvo, progressoAtual);
  }
}
