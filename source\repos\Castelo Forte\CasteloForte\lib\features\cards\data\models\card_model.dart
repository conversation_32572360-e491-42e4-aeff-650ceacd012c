/// Modelo de dados para cartão de crédito completo
class CardModel {
  final String id;
  final String nomeCartao;
  final String ultimosDigitos;
  final String bandeira;
  final bool ativo;
  final String? apelido;
  final double limite;
  final DateTime? dataVencimento;
  final DateTime dataCriacao;
  final DateTime? dataUltimaAtualizacao;

  const CardModel({
    required this.id,
    required this.nomeCartao,
    required this.ultimosDigitos,
    required this.bandeira,
    required this.ativo,
    this.apelido,
    required this.limite,
    this.dataVencimento,
    required this.dataCriacao,
    this.dataUltimaAtualizacao,
  });

  /// Cria uma instância a partir de JSON
  factory CardModel.fromJson(Map<String, dynamic> json) {
    return CardModel(
      id: json['id']?.toString() ?? '',
      nomeCartao: json['nomeCartao']?.toString() ?? '',
      ultimosDigitos: json['ultimosDigitos']?.toString() ?? '',
      bandeira: json['bandeira']?.toString() ?? '',
      ativo: json['ativo'] as bool? ?? true,
      apelido: json['apelido']?.toString(),
      limite: (json['limite'] as num?)?.toDouble() ?? 0.0,
      dataVencimento: json['dataVencimento'] != null
          ? DateTime.parse(json['dataVencimento'].toString())
          : null,
      dataCriacao: json['dataCriacao'] != null
          ? DateTime.parse(json['dataCriacao'].toString())
          : DateTime.now(),
      dataUltimaAtualizacao: json['dataUltimaAtualizacao'] != null
          ? DateTime.parse(json['dataUltimaAtualizacao'].toString())
          : null,
    );
  }

  /// Converte para JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nomeCartao': nomeCartao,
      'ultimosDigitos': ultimosDigitos,
      'bandeira': bandeira,
      'ativo': ativo,
      'apelido': apelido,
      'limite': limite,
      'dataVencimento': dataVencimento?.toIso8601String(),
      'dataCriacao': dataCriacao.toIso8601String(),
      'dataUltimaAtualizacao': dataUltimaAtualizacao?.toIso8601String(),
    };
  }

  /// Retorna o nome de exibição (apelido ou nome do cartão)
  String get nomeExibicao =>
      apelido?.isNotEmpty == true ? apelido! : nomeCartao;

  /// Retorna o número mascarado do cartão
  String get numeroMascarado => '**** **** **** $ultimosDigitos';

  /// Verifica se é VISA
  bool get isVisa => bandeira.toUpperCase() == 'VISA';

  /// Verifica se é Mastercard
  bool get isMastercard =>
      bandeira.toUpperCase() == 'MASTER' ||
      bandeira.toUpperCase() == 'MASTERCARD';

  /// Verifica se o cartão está vencido
  bool get isVencido {
    if (dataVencimento == null) return false;
    return DateTime.now().isAfter(dataVencimento!);
  }

  /// Verifica se o cartão vence em breve (próximos 30 dias)
  bool get venceEmBreve {
    if (dataVencimento == null) return false;
    final agora = DateTime.now();
    final em30Dias = agora.add(const Duration(days: 30));
    return dataVencimento!.isAfter(agora) && dataVencimento!.isBefore(em30Dias);
  }

  /// Retorna a data de vencimento formatada
  String get dataVencimentoFormatada {
    if (dataVencimento == null) return 'Não informado';
    return '${dataVencimento!.month.toString().padLeft(2, '0')}/${dataVencimento!.year}';
  }

  /// Cria uma cópia com campos modificados
  CardModel copyWith({
    String? id,
    String? nomeCartao,
    String? ultimosDigitos,
    String? bandeira,
    bool? ativo,
    String? apelido,
    double? limite,
    DateTime? dataVencimento,
    DateTime? dataCriacao,
    DateTime? dataUltimaAtualizacao,
  }) {
    return CardModel(
      id: id ?? this.id,
      nomeCartao: nomeCartao ?? this.nomeCartao,
      ultimosDigitos: ultimosDigitos ?? this.ultimosDigitos,
      bandeira: bandeira ?? this.bandeira,
      ativo: ativo ?? this.ativo,
      apelido: apelido ?? this.apelido,
      limite: limite ?? this.limite,
      dataVencimento: dataVencimento ?? this.dataVencimento,
      dataCriacao: dataCriacao ?? this.dataCriacao,
      dataUltimaAtualizacao:
          dataUltimaAtualizacao ?? this.dataUltimaAtualizacao,
    );
  }

  @override
  String toString() {
    return 'CardModel(id: $id, nomeCartao: $nomeCartao, ultimosDigitos: $ultimosDigitos, bandeira: $bandeira, ativo: $ativo, apelido: $apelido)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CardModel &&
        other.id == id &&
        other.nomeCartao == nomeCartao &&
        other.ultimosDigitos == ultimosDigitos &&
        other.bandeira == bandeira &&
        other.ativo == ativo &&
        other.apelido == apelido &&
        other.dataVencimento == dataVencimento &&
        other.dataCriacao == dataCriacao &&
        other.dataUltimaAtualizacao == dataUltimaAtualizacao;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      nomeCartao,
      ultimosDigitos,
      bandeira,
      ativo,
      apelido,
      dataVencimento,
      dataCriacao,
      dataUltimaAtualizacao,
    );
  }
}

/// Modelo para criação/edição de cartão
class CardFormModel {
  final String? id;
  final String nomeCartao;
  final String ultimosDigitos;
  final String bandeira;
  final String? apelido;
  final DateTime? dataVencimento;

  const CardFormModel({
    this.id,
    required this.nomeCartao,
    required this.ultimosDigitos,
    required this.bandeira,
    this.apelido,
    this.dataVencimento,
  });

  /// Converte para JSON para envio à API
  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      'nomeCartao': nomeCartao,
      'ultimosDigitos': ultimosDigitos,
      'bandeira': bandeira,
      'apelido': apelido,
      'dataVencimento': dataVencimento?.toIso8601String(),
    };
  }

  /// Cria uma instância a partir de CardModel para edição
  factory CardFormModel.fromCard(CardModel card) {
    return CardFormModel(
      id: card.id,
      nomeCartao: card.nomeCartao,
      ultimosDigitos: card.ultimosDigitos,
      bandeira: card.bandeira,
      apelido: card.apelido,
      dataVencimento: card.dataVencimento,
    );
  }
}
