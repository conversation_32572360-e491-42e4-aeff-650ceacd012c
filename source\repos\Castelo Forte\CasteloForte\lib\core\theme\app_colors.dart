import 'package:flutter/material.dart';

/// Cores da aplicação baseadas no tema oficial
class AppColors {
  // Cores principais
  static const Color primary = Color(0xFF002147); // Azul Marinho
  static const Color secondary = Color(0xFFC4A35A); // Dourado
  static const Color accent = Color(0xFF4A4A4A); // Cinza Chumbo

  // Cores de fundo
  static const Color background = Color(0xFF0A0A0A); // Fundo escuro
  static const Color surface = Color(0xFF1A1A2E); // Superfície
  static const Color cardBackground = Color(0xFF2A2A3E); // Fundo dos cards

  // Cores de texto
  static const Color textPrimary = Color(0xFFFDFDFD); // Branco Neve
  static const Color textSecondary = Color(0xFFB0B0B0); // Cinza claro
  static const Color textTertiary = Color(0xFF6E6E6E); // Cinza médio

  // Cores de status
  static const Color success = Color(0xFF4CAF50); // Verde
  static const Color error = Color(0xFFE53935); // Vermelho
  static const Color warning = Color(0xFFFFC107); // Amarelo
  static const Color info = Color(0xFF2196F3); // Azul

  // Cores específicas para elementos
  static const Color divider = Color(0xFF3A3A4E);
  static const Color border = Color(0xFF4A4A5E);
  static const Color disabled = Color(0xFF5A5A6E);

  // Gradientes
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, Color(0xFF003366)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient goldGradient = LinearGradient(
    colors: [secondary, Color(0xFFD4B36A)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}
