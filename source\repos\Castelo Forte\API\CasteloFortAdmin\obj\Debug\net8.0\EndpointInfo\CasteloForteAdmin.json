{
  "openapi": "3.0.1",
  "info": {
    "title": "Castelo Forte API",
    "version": "v1"
  },
  "paths": {
    "/api/Auth/login": {
      "post": {
        "tags": [
          "Auth"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/LoginRequestViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/LoginRequestViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/LoginRequestViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Auth/validate-token": {
      "post": {
        "tags": [
          "Auth"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ValidateTokenRequestViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/ValidateTokenRequestViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/ValidateTokenRequestViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Auth/refresh-token": {
      "post": {
        "tags": [
          "Auth"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/RefreshTokenRequestViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/RefreshTokenRequestViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/RefreshTokenRequestViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Auth/logout": {
      "post": {
        "tags": [
          "Auth"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/LogoutRequestViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/LogoutRequestViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/LogoutRequestViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Auth/me": {
      "get": {
        "tags": [
          "Auth"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Auth/status": {
      "get": {
        "tags": [
          "Auth"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/AuthAdmin/login": {
      "post": {
        "tags": [
          "AuthAdmin"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/LoginAdminRequestViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/LoginAdminRequestViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/LoginAdminRequestViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/AuthAdmin/validate-token": {
      "post": {
        "tags": [
          "AuthAdmin"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ValidateAdminTokenRequestViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/ValidateAdminTokenRequestViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/ValidateAdminTokenRequestViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/AuthAdmin/check-admin": {
      "get": {
        "tags": [
          "AuthAdmin"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/AuthAdmin/logout": {
      "post": {
        "tags": [
          "AuthAdmin"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Cartao": {
      "get": {
        "tags": [
          "Cartao"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "post": {
        "tags": [
          "Cartao"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CartaoViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/CartaoViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/CartaoViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Cartao/{id}": {
      "get": {
        "tags": [
          "Cartao"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "put": {
        "tags": [
          "Cartao"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CartaoViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/CartaoViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/CartaoViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "Cartao"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Cartao/filtrar/apelido/{apelido}": {
      "get": {
        "tags": [
          "Cartao"
        ],
        "parameters": [
          {
            "name": "apelido",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Cartao/filtrar/nome/{nome}": {
      "get": {
        "tags": [
          "Cartao"
        ],
        "parameters": [
          {
            "name": "nome",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Cartao/paginado": {
      "get": {
        "tags": [
          "Cartao"
        ],
        "parameters": [
          {
            "name": "page",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32",
              "default": 1
            }
          },
          {
            "name": "pageSize",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32",
              "default": 10
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Cartao/contagem": {
      "get": {
        "tags": [
          "Cartao"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Cartao/{id}/reativar": {
      "patch": {
        "tags": [
          "Cartao"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/health/mongodb": {
      "get": {
        "tags": [
          "CasteloForteAdmin"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Categoria": {
      "get": {
        "tags": [
          "Categoria"
        ],
        "parameters": [
          {
            "name": "ativa",
            "in": "query",
            "schema": {
              "type": "boolean",
              "default": true
            }
          },
          {
            "name": "tipo",
            "in": "query",
            "schema": {
              "$ref": "#/components/schemas/TipoCategoria"
            }
          },
          {
            "name": "search",
            "in": "query",
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": {
                    "$ref": "#/components/schemas/CategoriaViewModel"
                  }
                }
              }
            }
          }
        }
      },
      "post": {
        "tags": [
          "Categoria"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CategoriaCreateUpdateViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/CategoriaCreateUpdateViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/CategoriaCreateUpdateViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CategoriaViewModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/Categoria/{id}": {
      "get": {
        "tags": [
          "Categoria"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CategoriaViewModel"
                }
              }
            }
          }
        }
      },
      "put": {
        "tags": [
          "Categoria"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CategoriaCreateUpdateViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/CategoriaCreateUpdateViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/CategoriaCreateUpdateViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/CategoriaViewModel"
                }
              }
            }
          }
        }
      }
    },
    "/api/Categoria/select": {
      "get": {
        "tags": [
          "Categoria"
        ],
        "parameters": [
          {
            "name": "ativa",
            "in": "query",
            "schema": {
              "type": "boolean",
              "default": true
            }
          },
          {
            "name": "tipo",
            "in": "query",
            "schema": {
              "$ref": "#/components/schemas/TipoCategoria"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "application/json": {
                "schema": {
                  "type": "array",
                  "items": { }
                }
              }
            }
          }
        }
      }
    },
    "/api/Conta": {
      "get": {
        "tags": [
          "Conta"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "post": {
        "tags": [
          "Conta"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ContaViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/ContaViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/ContaViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Conta/{id}": {
      "get": {
        "tags": [
          "Conta"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "put": {
        "tags": [
          "Conta"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ContaViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/ContaViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/ContaViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "Conta"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Conta/tipo/{tipo}": {
      "get": {
        "tags": [
          "Conta"
        ],
        "parameters": [
          {
            "name": "tipo",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Conta/nome/{nome}": {
      "get": {
        "tags": [
          "Conta"
        ],
        "parameters": [
          {
            "name": "nome",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Conta/saldo-total": {
      "get": {
        "tags": [
          "Conta"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Conta/{id}/saldo": {
      "patch": {
        "tags": [
          "Conta"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "type": "number",
                "format": "double"
              }
            },
            "text/json": {
              "schema": {
                "type": "number",
                "format": "double"
              }
            },
            "application/*+json": {
              "schema": {
                "type": "number",
                "format": "double"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Dashboard/dados": {
      "get": {
        "tags": [
          "Dashboard"
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/DashboardResponseViewModel"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/DashboardResponseViewModel"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/DashboardResponseViewModel"
                }
              }
            }
          },
          "401": {
            "description": "Unauthorized",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ProblemDetails"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProblemDetails"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Internal Server Error"
          }
        }
      }
    },
    "/api/Dashboard/questionario-perfil": {
      "put": {
        "tags": [
          "Dashboard"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateQuestionarioPerfilRequest"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateQuestionarioPerfilRequest"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateQuestionarioPerfilRequest"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": { }
              },
              "application/json": {
                "schema": { }
              },
              "text/json": {
                "schema": { }
              }
            }
          },
          "400": {
            "description": "Bad Request",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ProblemDetails"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProblemDetails"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProblemDetails"
                }
              }
            }
          },
          "401": {
            "description": "Unauthorized",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ProblemDetails"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProblemDetails"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ProblemDetails"
                }
              }
            }
          },
          "500": {
            "description": "Internal Server Error"
          }
        }
      }
    },
    "/api/Dashboard/diagnostico-criptografia": {
      "get": {
        "tags": [
          "Dashboard"
        ],
        "responses": {
          "200": {
            "description": "OK"
          },
          "500": {
            "description": "Internal Server Error"
          }
        }
      }
    },
    "/api/Dashboard/diagnostico": {
      "get": {
        "tags": [
          "Dashboard"
        ],
        "responses": {
          "200": {
            "description": "OK"
          },
          "500": {
            "description": "Internal Server Error"
          }
        }
      }
    },
    "/api/HistoricoAdmin/filtrados": {
      "post": {
        "tags": [
          "HistoricoAdmin"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/FiltroHistoricoViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/FiltroHistoricoViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/FiltroHistoricoViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/HistoricoAdmin/acao/{acao}": {
      "get": {
        "tags": [
          "HistoricoAdmin"
        ],
        "parameters": [
          {
            "name": "acao",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/HistoricoAdmin/usuario/{idUsuario}": {
      "get": {
        "tags": [
          "HistoricoAdmin"
        ],
        "parameters": [
          {
            "name": "idUsuario",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/HistoricoAdmin/estatisticas": {
      "get": {
        "tags": [
          "HistoricoAdmin"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/HistoricoAdmin/todos": {
      "get": {
        "tags": [
          "HistoricoAdmin"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/HistoricoAdmin/periodo": {
      "get": {
        "tags": [
          "HistoricoAdmin"
        ],
        "parameters": [
          {
            "name": "dataInicio",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "dataFim",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/LogErroAdmin/filtrados": {
      "post": {
        "tags": [
          "LogErroAdmin"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/FiltroLogErroViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/FiltroLogErroViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/FiltroLogErroViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/LogErroAdmin/controller/{controller}": {
      "get": {
        "tags": [
          "LogErroAdmin"
        ],
        "parameters": [
          {
            "name": "controller",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/LogErroAdmin/usuario/{idUsuario}": {
      "get": {
        "tags": [
          "LogErroAdmin"
        ],
        "parameters": [
          {
            "name": "idUsuario",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/LogErroAdmin/estatisticas": {
      "get": {
        "tags": [
          "LogErroAdmin"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/LogErroAdmin/todos": {
      "get": {
        "tags": [
          "LogErroAdmin"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/LogErroAdmin/periodo": {
      "get": {
        "tags": [
          "LogErroAdmin"
        ],
        "parameters": [
          {
            "name": "dataInicio",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "dataFim",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Meta/{id}": {
      "get": {
        "tags": [
          "Meta"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "Meta"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Meta": {
      "get": {
        "tags": [
          "Meta"
        ],
        "parameters": [
          {
            "name": "Status",
            "in": "query",
            "schema": {
              "$ref": "#/components/schemas/MetaStatus"
            }
          },
          {
            "name": "DataInicio",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date"
            }
          },
          {
            "name": "DataFim",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date"
            }
          },
          {
            "name": "DataVencimentoInicio",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date"
            }
          },
          {
            "name": "DataVencimentoFim",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date"
            }
          },
          {
            "name": "CategoriasIds",
            "in": "query",
            "schema": {
              "type": "array",
              "items": {
                "type": "string"
              }
            }
          },
          {
            "name": "ApenasAtivas",
            "in": "query",
            "schema": {
              "type": "boolean"
            }
          },
          {
            "name": "ApenasMensais",
            "in": "query",
            "schema": {
              "type": "boolean"
            }
          },
          {
            "name": "ValorAlvoMinimo",
            "in": "query",
            "schema": {
              "minimum": 0,
              "type": "number",
              "format": "double"
            }
          },
          {
            "name": "ValorAlvoMaximo",
            "in": "query",
            "schema": {
              "minimum": 0,
              "type": "number",
              "format": "double"
            }
          },
          {
            "name": "ProgressoMinimo",
            "in": "query",
            "schema": {
              "maximum": 100,
              "minimum": 0,
              "type": "number",
              "format": "double"
            }
          },
          {
            "name": "ProgressoMaximo",
            "in": "query",
            "schema": {
              "maximum": 100,
              "minimum": 0,
              "type": "number",
              "format": "double"
            }
          },
          {
            "name": "TextoBusca",
            "in": "query",
            "schema": {
              "maxLength": 100,
              "minLength": 0,
              "type": "string"
            }
          },
          {
            "name": "Ordenacao",
            "in": "query",
            "schema": {
              "$ref": "#/components/schemas/MetaOrdenacao"
            }
          },
          {
            "name": "OrdenacaoDecrescente",
            "in": "query",
            "schema": {
              "type": "boolean"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "post": {
        "tags": [
          "Meta"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/MetaViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/MetaViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/MetaViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "put": {
        "tags": [
          "Meta"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/MetaViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/MetaViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/MetaViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Meta/filter-options": {
      "get": {
        "tags": [
          "Meta"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Migration/add-admin-flag": {
      "post": {
        "tags": [
          "Migration"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            },
            "text/json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            },
            "application/*+json": {
              "schema": {
                "type": "array",
                "items": {
                  "type": "string"
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Migration/check-admin-status": {
      "get": {
        "tags": [
          "Migration"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/Migration/promote-to-admin/{usuarioId}": {
      "post": {
        "tags": [
          "Migration"
        ],
        "parameters": [
          {
            "name": "usuarioId",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/PlanoFinanceiro": {
      "get": {
        "tags": [
          "PlanoFinanceiro"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "post": {
        "tags": [
          "PlanoFinanceiro"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/PlanoFinanceiroViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/PlanoFinanceiroViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/PlanoFinanceiroViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/PlanoFinanceiro/{id}": {
      "get": {
        "tags": [
          "PlanoFinanceiro"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "put": {
        "tags": [
          "PlanoFinanceiro"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/PlanoFinanceiroViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/PlanoFinanceiroViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/PlanoFinanceiroViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      },
      "delete": {
        "tags": [
          "PlanoFinanceiro"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/PlanoFinanceiro/titulo/{titulo}": {
      "get": {
        "tags": [
          "PlanoFinanceiro"
        ],
        "parameters": [
          {
            "name": "titulo",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/PlanoFinanceiro/periodo": {
      "get": {
        "tags": [
          "PlanoFinanceiro"
        ],
        "parameters": [
          {
            "name": "dataInicio",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          },
          {
            "name": "dataFim",
            "in": "query",
            "schema": {
              "type": "string",
              "format": "date-time"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/PlanoFinanceiro/{id}/duplicar": {
      "post": {
        "tags": [
          "PlanoFinanceiro"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "type": "string"
              }
            },
            "text/json": {
              "schema": {
                "type": "string"
              }
            },
            "application/*+json": {
              "schema": {
                "type": "string"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/PlanoFinanceiro/{id}/arquivar": {
      "patch": {
        "tags": [
          "PlanoFinanceiro"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/PlanoFinanceiro/{id}/restaurar": {
      "patch": {
        "tags": [
          "PlanoFinanceiro"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/UsuarioAdmin/filtrados": {
      "post": {
        "tags": [
          "UsuarioAdmin"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/FiltroUsuarioViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/FiltroUsuarioViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/FiltroUsuarioViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/UsuarioAdmin/{id}": {
      "get": {
        "tags": [
          "UsuarioAdmin"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/UsuarioAdmin/estatisticas": {
      "get": {
        "tags": [
          "UsuarioAdmin"
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/UsuarioPublico/cadastrar": {
      "post": {
        "tags": [
          "UsuarioPublico"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CadastroUsuarioViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/CadastroUsuarioViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/CadastroUsuarioViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/api/UsuarioPublico/legacy": {
      "post": {
        "tags": [
          "UsuarioPublico"
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UsuarioViewModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/UsuarioViewModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/UsuarioViewModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK"
          }
        },
        "deprecated": true
      }
    }
  },
  "components": {
    "schemas": {
      "AccountType": {
        "enum": [
          1,
          2,
          3
        ],
        "type": "integer",
        "format": "int32"
      },
      "CadastroUsuarioViewModel": {
        "required": [
          "celular",
          "cpf",
          "dtaNascimento",
          "email",
          "flgTermosECondicoes",
          "nome",
          "senha"
        ],
        "type": "object",
        "properties": {
          "nome": {
            "maxLength": 100,
            "minLength": 2,
            "type": "string"
          },
          "email": {
            "maxLength": 100,
            "minLength": 0,
            "type": "string",
            "format": "email"
          },
          "cpf": {
            "maxLength": 14,
            "minLength": 11,
            "type": "string"
          },
          "celular": {
            "maxLength": 15,
            "minLength": 10,
            "type": "string"
          },
          "dtaNascimento": {
            "type": "string",
            "format": "date-time"
          },
          "senha": {
            "maxLength": 100,
            "minLength": 8,
            "type": "string"
          },
          "flgTermosECondicoes": {
            "type": "boolean"
          },
          "flgDoisFatores": {
            "type": "boolean"
          },
          "tokenAcesso": {
            "type": "string",
            "nullable": true
          },
          "nomeBaseDados": {
            "type": "string",
            "nullable": true
          },
          "connectionString": {
            "type": "string",
            "nullable": true
          },
          "idPerfilFinanceiro": {
            "type": "string",
            "nullable": true
          },
          "dtaTermosECondicoes": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "dtaTokenAcessoGerado": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "dtaUltimoAcesso": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "id": {
            "type": "string",
            "nullable": true
          },
          "dtaCadastro": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "flgAtivo": {
            "type": "boolean"
          },
          "coracaoInquieto": {
            "maximum": 100,
            "minimum": 0,
            "type": "integer",
            "format": "int32"
          },
          "construtorAnalitico": {
            "maximum": 100,
            "minimum": 0,
            "type": "integer",
            "format": "int32"
          },
          "visionarioOusado": {
            "maximum": 100,
            "minimum": 0,
            "type": "integer",
            "format": "int32"
          },
          "exploradorGeneroso": {
            "maximum": 100,
            "minimum": 0,
            "type": "integer",
            "format": "int32"
          },
          "estrategistaConsciente": {
            "maximum": 100,
            "minimum": 0,
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "CartaoViewModel": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "nullable": true
          },
          "dtaCadastro": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "flgAtivo": {
            "type": "boolean"
          },
          "numeroCartao": {
            "type": "string",
            "nullable": true
          },
          "nomeCartao": {
            "type": "string",
            "nullable": true
          },
          "dtaValidade": {
            "type": "string",
            "format": "date-time"
          },
          "cvv": {
            "type": "string",
            "nullable": true
          },
          "diaFechamento": {
            "type": "integer",
            "format": "int32"
          },
          "diaVencimento": {
            "type": "integer",
            "format": "int32"
          },
          "apelidoCartao": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "CategoriaCreateUpdateViewModel": {
        "required": [
          "cor",
          "nome",
          "tipo"
        ],
        "type": "object",
        "properties": {
          "nome": {
            "maxLength": 100,
            "minLength": 2,
            "type": "string"
          },
          "descricao": {
            "maxLength": 500,
            "minLength": 0,
            "type": "string",
            "nullable": true
          },
          "tipo": {
            "$ref": "#/components/schemas/TipoCategoria"
          },
          "cor": {
            "minLength": 1,
            "pattern": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$",
            "type": "string"
          },
          "icone": {
            "type": "integer",
            "format": "int32"
          },
          "ordem": {
            "maximum": 2147483647,
            "minimum": 0,
            "type": "integer",
            "format": "int32"
          },
          "limiteGastos": {
            "minimum": 0,
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "periodoLimite": {
            "$ref": "#/components/schemas/PeriodoLimite"
          },
          "observacoes": {
            "maxLength": 1000,
            "minLength": 0,
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "CategoriaViewModel": {
        "required": [
          "nome",
          "tipo"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "nullable": true
          },
          "nome": {
            "maxLength": 100,
            "minLength": 0,
            "type": "string"
          },
          "descricao": {
            "maxLength": 500,
            "minLength": 0,
            "type": "string",
            "nullable": true
          },
          "tipo": {
            "$ref": "#/components/schemas/TipoCategoria"
          },
          "cor": {
            "type": "string",
            "nullable": true
          },
          "icone": {
            "type": "integer",
            "format": "int32"
          },
          "ativa": {
            "type": "boolean"
          },
          "dataCriacao": {
            "type": "string",
            "format": "date-time"
          },
          "dataAlteracao": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "ordem": {
            "type": "integer",
            "format": "int32"
          },
          "limiteGastos": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "periodoLimite": {
            "$ref": "#/components/schemas/PeriodoLimite"
          },
          "observacoes": {
            "type": "string",
            "nullable": true
          },
          "numeroTransacoes": {
            "type": "integer",
            "format": "int32"
          },
          "numeroMetas": {
            "type": "integer",
            "format": "int32"
          },
          "valorGasto": {
            "type": "number",
            "format": "double"
          },
          "percentualLimite": {
            "type": "number",
            "format": "double",
            "nullable": true,
            "readOnly": true
          },
          "limiteUltrapassado": {
            "type": "boolean",
            "readOnly": true
          }
        },
        "additionalProperties": false
      },
      "ContaViewModel": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "nullable": true
          },
          "dtaCadastro": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "flgAtivo": {
            "type": "boolean"
          },
          "nome": {
            "type": "string",
            "nullable": true
          },
          "apelidoConta": {
            "type": "string",
            "nullable": true
          },
          "tipoConta": {
            "$ref": "#/components/schemas/AccountType"
          },
          "tipoContaDescricao": {
            "type": "string",
            "nullable": true,
            "readOnly": true
          },
          "saldo": {
            "type": "number",
            "format": "double"
          },
          "ativa": {
            "type": "boolean"
          },
          "dtaAlteracao": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "ultimosDigitos": {
            "type": "string",
            "nullable": true
          },
          "dataValidade": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "diaFechamento": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "diaVencimento": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "bandeira": {
            "type": "string",
            "nullable": true
          },
          "limiteTotal": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "limiteUtilizado": {
            "type": "number",
            "format": "double",
            "nullable": true
          },
          "isCartaoCredito": {
            "type": "boolean",
            "readOnly": true
          },
          "limiteDisponivel": {
            "type": "number",
            "format": "double",
            "readOnly": true
          },
          "nomeExibicao": {
            "type": "string",
            "nullable": true,
            "readOnly": true
          },
          "numeroMascarado": {
            "type": "string",
            "nullable": true,
            "readOnly": true
          }
        },
        "additionalProperties": false
      },
      "DashboardCartaoViewModel": {
        "required": [
          "ativo",
          "bandeira",
          "id",
          "nomeCartao",
          "ultimosDigitos"
        ],
        "type": "object",
        "properties": {
          "id": {
            "minLength": 1,
            "type": "string"
          },
          "nomeCartao": {
            "maxLength": 100,
            "minLength": 0,
            "type": "string"
          },
          "ultimosDigitos": {
            "maxLength": 4,
            "minLength": 4,
            "type": "string"
          },
          "bandeira": {
            "maxLength": 20,
            "minLength": 0,
            "type": "string"
          },
          "ativo": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "DashboardCategoriaViewModel": {
        "required": [
          "cor",
          "icone",
          "id",
          "nome",
          "tipo"
        ],
        "type": "object",
        "properties": {
          "id": {
            "minLength": 1,
            "type": "string"
          },
          "nome": {
            "maxLength": 100,
            "minLength": 0,
            "type": "string"
          },
          "icone": {
            "maxLength": 50,
            "minLength": 0,
            "type": "string"
          },
          "cor": {
            "maxLength": 7,
            "minLength": 0,
            "type": "string"
          },
          "tipo": {
            "maxLength": 10,
            "minLength": 0,
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "DashboardContaViewModel": {
        "required": [
          "ativa",
          "id",
          "nomeBanco",
          "saldo",
          "tipoConta"
        ],
        "type": "object",
        "properties": {
          "id": {
            "minLength": 1,
            "type": "string"
          },
          "nomeBanco": {
            "maxLength": 100,
            "minLength": 0,
            "type": "string"
          },
          "tipoConta": {
            "maxLength": 50,
            "minLength": 0,
            "type": "string"
          },
          "saldo": {
            "type": "number",
            "format": "double"
          },
          "ativa": {
            "type": "boolean"
          },
          "apelido": {
            "type": "string",
            "nullable": true
          },
          "ultimosDigitos": {
            "type": "string",
            "nullable": true
          },
          "bandeira": {
            "type": "string",
            "nullable": true
          },
          "nomeExibicao": {
            "type": "string",
            "nullable": true,
            "readOnly": true
          },
          "isCartaoCredito": {
            "type": "boolean",
            "readOnly": true
          },
          "numeroMascarado": {
            "type": "string",
            "nullable": true,
            "readOnly": true
          }
        },
        "additionalProperties": false
      },
      "DashboardLancamentoViewModel": {
        "required": [
          "categoria",
          "data",
          "descricao",
          "id",
          "tipo",
          "valor"
        ],
        "type": "object",
        "properties": {
          "id": {
            "minLength": 1,
            "type": "string"
          },
          "descricao": {
            "maxLength": 200,
            "minLength": 0,
            "type": "string"
          },
          "valor": {
            "type": "number",
            "format": "double"
          },
          "data": {
            "type": "string",
            "format": "date-time"
          },
          "tipo": {
            "maxLength": 10,
            "minLength": 0,
            "type": "string"
          },
          "categoria": {
            "maxLength": 100,
            "minLength": 0,
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "DashboardResponseViewModel": {
        "required": [
          "cartoes",
          "categorias",
          "contas",
          "exibirQuestionarioPerfil",
          "ultimosLancamentos",
          "valorTotal"
        ],
        "type": "object",
        "properties": {
          "valorTotal": {
            "type": "number",
            "format": "double"
          },
          "exibirQuestionarioPerfil": {
            "type": "boolean"
          },
          "contas": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/DashboardContaViewModel"
            }
          },
          "cartoes": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/DashboardCartaoViewModel"
            }
          },
          "ultimosLancamentos": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/DashboardLancamentoViewModel"
            }
          },
          "categorias": {
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/DashboardCategoriaViewModel"
            }
          }
        },
        "additionalProperties": false
      },
      "FiltroHistoricoViewModel": {
        "type": "object",
        "properties": {
          "dataInicio": {
            "type": "string",
            "format": "date",
            "nullable": true
          },
          "dataFim": {
            "type": "string",
            "format": "date",
            "nullable": true
          },
          "idUsuario": {
            "type": "string",
            "nullable": true
          },
          "metodo": {
            "type": "string",
            "nullable": true
          },
          "acao": {
            "type": "string",
            "nullable": true
          },
          "textoBusca": {
            "type": "string",
            "nullable": true
          },
          "pagina": {
            "type": "integer",
            "format": "int32"
          },
          "itensPorPagina": {
            "type": "integer",
            "format": "int32"
          },
          "ordenarPor": {
            "type": "string",
            "nullable": true
          },
          "direcaoOrdenacao": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "FiltroLogErroViewModel": {
        "type": "object",
        "properties": {
          "dataInicio": {
            "type": "string",
            "format": "date",
            "nullable": true
          },
          "dataFim": {
            "type": "string",
            "format": "date",
            "nullable": true
          },
          "idUsuario": {
            "type": "string",
            "nullable": true
          },
          "controller": {
            "type": "string",
            "nullable": true
          },
          "metodo": {
            "type": "string",
            "nullable": true
          },
          "textoErro": {
            "type": "string",
            "nullable": true
          },
          "pagina": {
            "type": "integer",
            "format": "int32"
          },
          "itensPorPagina": {
            "type": "integer",
            "format": "int32"
          },
          "ordenarPor": {
            "type": "string",
            "nullable": true
          },
          "direcaoOrdenacao": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "FiltroUsuarioViewModel": {
        "type": "object",
        "properties": {
          "nome": {
            "type": "string",
            "nullable": true
          },
          "email": {
            "type": "string",
            "nullable": true
          },
          "flgAtivo": {
            "type": "boolean",
            "nullable": true
          },
          "dataCadastroInicio": {
            "type": "string",
            "format": "date",
            "nullable": true
          },
          "dataCadastroFim": {
            "type": "string",
            "format": "date",
            "nullable": true
          },
          "ultimoAcessoInicio": {
            "type": "string",
            "format": "date",
            "nullable": true
          },
          "ultimoAcessoFim": {
            "type": "string",
            "format": "date",
            "nullable": true
          },
          "textoBusca": {
            "type": "string",
            "nullable": true
          },
          "pagina": {
            "type": "integer",
            "format": "int32"
          },
          "itensPorPagina": {
            "type": "integer",
            "format": "int32"
          },
          "ordenarPor": {
            "type": "string",
            "nullable": true
          },
          "direcaoOrdenacao": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "LoginAdminRequestViewModel": {
        "required": [
          "cpf",
          "senha"
        ],
        "type": "object",
        "properties": {
          "cpf": {
            "maxLength": 14,
            "minLength": 11,
            "type": "string"
          },
          "email": {
            "type": "string",
            "format": "email",
            "nullable": true
          },
          "senha": {
            "maxLength": 100,
            "minLength": 6,
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "LoginRequestViewModel": {
        "required": [
          "cpf",
          "senha"
        ],
        "type": "object",
        "properties": {
          "cpf": {
            "maxLength": 14,
            "minLength": 11,
            "type": "string"
          },
          "email": {
            "type": "string",
            "format": "email",
            "nullable": true
          },
          "senha": {
            "maxLength": 100,
            "minLength": 6,
            "type": "string"
          },
          "lembrarLogin": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "LogoutRequestViewModel": {
        "type": "object",
        "properties": {
          "token": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "MetaOrdenacao": {
        "enum": [
          1,
          2,
          3,
          4,
          5,
          6,
          7
        ],
        "type": "integer",
        "format": "int32"
      },
      "MetaStatus": {
        "enum": [
          1,
          2,
          3,
          4
        ],
        "type": "integer",
        "format": "int32"
      },
      "MetaViewModel": {
        "required": [
          "dataConclusao",
          "nome",
          "valorAlvo"
        ],
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "nullable": true
          },
          "dtaCadastro": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "flgAtivo": {
            "type": "boolean"
          },
          "nome": {
            "maxLength": 100,
            "minLength": 0,
            "type": "string"
          },
          "descricao": {
            "maxLength": 500,
            "minLength": 0,
            "type": "string",
            "nullable": true
          },
          "dataAbertura": {
            "type": "string",
            "format": "date-time"
          },
          "dataConclusao": {
            "type": "string",
            "format": "date-time"
          },
          "dataConclusaoReal": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "valorAlvo": {
            "minimum": 0,01,
            "type": "number",
            "format": "double"
          },
          "progressoAtual": {
            "type": "number",
            "format": "double"
          },
          "status": {
            "$ref": "#/components/schemas/MetaStatus"
          },
          "statusDescricao": {
            "type": "string",
            "nullable": true,
            "readOnly": true
          },
          "iconeMeta": {
            "type": "string",
            "nullable": true
          },
          "corMeta": {
            "type": "string",
            "nullable": true
          },
          "categoriasAssociadas": {
            "type": "array",
            "items": {
              "type": "string"
            },
            "nullable": true
          },
          "isMetaMensal": {
            "type": "boolean"
          },
          "percentualProgresso": {
            "type": "number",
            "format": "double",
            "readOnly": true
          },
          "valorRestante": {
            "type": "number",
            "format": "double",
            "readOnly": true
          },
          "isAtingida": {
            "type": "boolean",
            "readOnly": true
          },
          "isVencida": {
            "type": "boolean",
            "readOnly": true
          },
          "diasRestantes": {
            "type": "integer",
            "format": "int32",
            "readOnly": true
          },
          "nomeMeta": {
            "type": "string",
            "nullable": true,
            "deprecated": true
          },
          "flgMensal": {
            "type": "boolean",
            "deprecated": true
          },
          "valorObjetivo": {
            "type": "number",
            "format": "double",
            "deprecated": true
          },
          "valorAtual": {
            "type": "number",
            "format": "double",
            "deprecated": true
          },
          "dtaAlteracao": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "PeriodoLimite": {
        "enum": [
          1,
          2,
          3,
          4
        ],
        "type": "integer",
        "format": "int32"
      },
      "PlanoFinanceiroViewModel": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "nullable": true
          },
          "dtaCadastro": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "flgAtivo": {
            "type": "boolean"
          },
          "titulo": {
            "type": "string",
            "nullable": true
          },
          "descricao": {
            "type": "string",
            "nullable": true
          },
          "dtaAlteracao": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          }
        },
        "additionalProperties": false
      },
      "ProblemDetails": {
        "type": "object",
        "properties": {
          "type": {
            "type": "string",
            "nullable": true
          },
          "title": {
            "type": "string",
            "nullable": true
          },
          "status": {
            "type": "integer",
            "format": "int32",
            "nullable": true
          },
          "detail": {
            "type": "string",
            "nullable": true
          },
          "instance": {
            "type": "string",
            "nullable": true
          }
        },
        "additionalProperties": { }
      },
      "RefreshTokenRequestViewModel": {
        "required": [
          "token"
        ],
        "type": "object",
        "properties": {
          "token": {
            "minLength": 1,
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "TipoCategoria": {
        "enum": [
          1,
          2,
          3
        ],
        "type": "integer",
        "format": "int32"
      },
      "UpdateQuestionarioPerfilRequest": {
        "type": "object",
        "properties": {
          "exibirQuestionarioPerfil": {
            "type": "boolean"
          }
        },
        "additionalProperties": false
      },
      "UsuarioViewModel": {
        "type": "object",
        "properties": {
          "id": {
            "type": "string",
            "nullable": true
          },
          "dtaCadastro": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "flgAtivo": {
            "type": "boolean"
          },
          "nome": {
            "type": "string",
            "nullable": true
          },
          "email": {
            "type": "string",
            "nullable": true
          },
          "cpf": {
            "type": "string",
            "nullable": true
          },
          "celular": {
            "type": "string",
            "nullable": true
          },
          "dtaNascimento": {
            "type": "string",
            "format": "date-time"
          },
          "flgTermosECondicoes": {
            "type": "boolean"
          },
          "dtaTermosECondicoes": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "dtaUltimoAcesso": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "tokenAcesso": {
            "type": "string",
            "nullable": true
          },
          "dtaTokenAcessoGerado": {
            "type": "string",
            "format": "date-time",
            "nullable": true
          },
          "senha": {
            "type": "string",
            "nullable": true
          },
          "flgDoisFatores": {
            "type": "boolean"
          },
          "flgAdministrador": {
            "type": "boolean"
          },
          "connectionString": {
            "type": "string",
            "nullable": true
          },
          "nomeBaseDados": {
            "type": "string",
            "nullable": true
          },
          "idPerfilFinanceiro": {
            "type": "string",
            "nullable": true
          },
          "coracaoInquieto": {
            "type": "integer",
            "format": "int32"
          },
          "construtorAnalitico": {
            "type": "integer",
            "format": "int32"
          },
          "visionarioOusado": {
            "type": "integer",
            "format": "int32"
          },
          "exploradorGeneroso": {
            "type": "integer",
            "format": "int32"
          },
          "estrategistaConsciente": {
            "type": "integer",
            "format": "int32"
          }
        },
        "additionalProperties": false
      },
      "ValidateAdminTokenRequestViewModel": {
        "required": [
          "token"
        ],
        "type": "object",
        "properties": {
          "token": {
            "minLength": 1,
            "type": "string"
          }
        },
        "additionalProperties": false
      },
      "ValidateTokenRequestViewModel": {
        "required": [
          "token"
        ],
        "type": "object",
        "properties": {
          "token": {
            "minLength": 1,
            "type": "string"
          }
        },
        "additionalProperties": false
      }
    },
    "securitySchemes": {
      "Bearer": {
        "type": "apiKey",
        "description": "JWT Authorization header using the Bearer scheme. Example: \"Authorization: Bearer {token}\"",
        "name": "Authorization",
        "in": "header"
      }
    }
  },
  "security": [
    {
      "Bearer": [ ]
    }
  ]
}