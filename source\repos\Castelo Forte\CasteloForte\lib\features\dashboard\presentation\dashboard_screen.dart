import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/widgets/app_header.dart';
import '../../../core/widgets/app_footer.dart';
import '../../../core/widgets/dashboard_cards.dart';
import '../../../core/widgets/dashboard_section.dart';
import '../../../core/utils/navigation_helper.dart';
import '../../../core/utils/constants.dart';
import '../../../core/services/app_lifecycle_manager.dart';
import '../data/dashboard_service.dart';
import '../data/models/dashboard_response_model.dart';
import '../../categories/presentation/categories_list_screen.dart';
import '../../goals/presentation/goals_list_screen.dart';
import '../../goals/data/goals_service.dart';
import '../../goals/models/goal_model.dart';
import '../../banking/presentation/bank_connection_screen.dart';
import '../../accounts/presentation/account_details_screen.dart';
import '../../accounts/presentation/add_account_screen.dart';
import '../../cards/presentation/card_details_screen.dart';
import '../../cards/presentation/add_card_screen.dart';
import '../../cards/presentation/cards_list_screen.dart';
import '../../transactions/presentation/transaction_details_screen.dart';
import '../../transactions/presentation/add_transaction_screen.dart';
import '../../categories/data/categories_service.dart';
import '../../categories/models/category_model.dart';

/// Tela de Dashboard
class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  DashboardResponseModel? _dashboardData;
  List<GoalModel> _goals = [];
  List<CategoryModel> _categories = [];
  bool _isLoading = true;
  bool _showBalance = true;
  bool _isFromApi = true;
  String? _connectionMessage;

  @override
  void initState() {
    super.initState();

    // Registra a rota atual no gerenciador de ciclo de vida
    AppLifecycleManager.instance.setCurrentRoute(AppConstants.dashboardRoute);

    _loadDashboardData();
  }

  /// Carrega os dados da dashboard
  Future<void> _loadDashboardData() async {
    try {
      setState(() {
        _isLoading = true;
        _connectionMessage = null;
      });

      // Carrega dados da dashboard, metas e categorias em paralelo
      final results = await Future.wait([
        DashboardService.getDashboardData(),
        GoalsService.getAllGoals(),
        CategoriesService.getActiveCategories(),
      ]);

      final dashboardResult = results[0] as DashboardLoadResult;
      final goals = results[1] as List<GoalModel>;
      final categories = results[2] as List<CategoryModel>;

      if (mounted) {
        setState(() {
          _dashboardData = dashboardResult.data; // Pode ser null se API falhou
          _goals = goals;
          _categories = categories;
          _isFromApi = dashboardResult.isFromApi;
          _connectionMessage = dashboardResult.errorMessage;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _connectionMessage = 'Erro inesperado ao carregar dados: $e';
          _isLoading = false;
        });
      }
    }
  }

  /// Recarrega os dados da dashboard
  Future<void> _refreshData() async {
    await _loadDashboardData();
  }

  /// Widget de loading para o card de saldo
  Widget _buildLoadingCard() {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: AppTheme.charcoalGrayColor.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(16),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.goldColor),
        ),
      ),
    );
  }

  /// Widget de mensagem de conexão bonita
  Widget _buildConnectionMessage() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.goldColor.withValues(alpha: 0.1),
            AppTheme.goldColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.goldColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.goldColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.cloud_off_outlined,
              color: AppTheme.goldColor,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Modo Offline',
                  style: TextStyle(
                    color: AppTheme.goldColor,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _connectionMessage!,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: _refreshData,
            icon: const Icon(
              Icons.refresh,
              color: AppTheme.goldColor,
              size: 20,
            ),
            tooltip: 'Tentar conectar novamente',
          ),
        ],
      ),
    );
  }

  /// Widget de estado vazio quando não há dados da API
  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: AppTheme.charcoalGrayColor.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(
              Icons.account_balance_wallet_outlined,
              color: AppTheme.goldColor,
              size: 64,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'Dashboard Indisponível',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 12),
          Text(
            'Não foi possível carregar os dados da sua dashboard.\nVerifique sua conexão e tente novamente.',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 14,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton.icon(
            onPressed: _refreshData,
            icon: const Icon(Icons.refresh),
            label: const Text('Tentar Novamente'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.goldColor,
              foregroundColor: AppTheme.navyBlueColor,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.navyBlueColor,
      body: SafeArea(
        child: Column(
          children: [
            // Header com avatar, saudação e botões
            const AppHeader(),

            // Conteúdo principal
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 8),

                    // Mensagem de conexão (se não estiver conectado à API)
                    if (!_isFromApi && _connectionMessage != null) ...[
                      _buildConnectionMessage(),
                      const SizedBox(height: 16),
                    ],

                    // Conteúdo da dashboard
                    if (_isLoading)
                      _buildLoadingCard()
                    else if (_dashboardData != null) ...[
                      // a) Saldo Disponível
                      BalanceCard(
                        balance: _dashboardData!.valorTotal,
                        showBalance: _showBalance,
                        onToggleVisibility: () {
                          setState(() {
                            _showBalance = !_showBalance;
                          });
                        },
                      ),

                      const SizedBox(height: 24),

                      // b) Perfil Financeiro (condicional)
                      if (_dashboardData!.exibirQuestionarioPerfil) ...[
                        const FinancialProfileCard(),
                        const SizedBox(height: 24),
                      ],

                      // c) Últimos Lançamentos
                      _buildTransactionsSection(),

                      const SizedBox(height: 24),

                      // d) Contas
                      _buildAccountsSection(),

                      const SizedBox(height: 24),

                      // e) Meus Cartões
                      _buildCardsSection(),

                      const SizedBox(height: 24),

                      // f) Metas Financeiras
                      _buildGoalsSection(),

                      const SizedBox(height: 24),

                      // h) Categorias
                      _buildCategoriesSection(),

                      const SizedBox(height: 24),

                      // Conexão Bancária (adicional)
                      BankConnectionCard(
                        onConnect: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  const BankConnectionScreen(),
                            ),
                          );
                        },
                      ),
                    ] else
                      _buildEmptyState(),

                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      // Footer com botões de navegação
      bottomNavigationBar: const AppFooter(currentIndex: 0),
    );
  }

  /// Constrói a seção de contas
  Widget _buildAccountsSection() {
    // Se não há dados, não exibe a seção
    if (_dashboardData == null) return const SizedBox.shrink();

    final contas = _dashboardData!.contasAtivas;

    // Se não há contas, mostra seção vazia
    if (contas.isEmpty) {
      return _buildEmptySection(
        'Contas',
        'Nenhuma conta cadastrada',
        'Adicione suas contas bancárias para começar a gerenciar suas finanças',
        Icons.account_balance_outlined,
      );
    }

    // Cria lista de cards incluindo o card de ação
    final List<Widget> accountCards = contas
        .take(3)
        .map<Widget>(
          (conta) => AccountCard(
            bankName: conta.nomeBanco,
            accountType: conta.tipoConta,
            balance: conta.saldo,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => AccountDetailsScreen(
                    account: {
                      'id': conta.id,
                      'nomeBanco': conta.nomeBanco,
                      'tipoConta': conta.tipoConta,
                      'saldo': conta.saldo,
                      'ativa': conta.ativa,
                      'apelido': '',
                      'dataCriacao': DateTime.now(),
                      'dataUltimaAtualizacao': DateTime.now(),
                    },
                  ),
                ),
              );
            },
          ),
        )
        .toList();

    // Adiciona o card de ação no final se há espaço
    if (contas.length < 3) {
      accountCards.add(
        AddAccountCard(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const AddAccountScreen()),
            );
          },
        ),
      );
    }

    return DashboardSection(
      title: 'Contas',
      onSeeAll: () => NavigationHelper.goToAccounts(context),
      content: HorizontalCardList(height: 140, cards: accountCards),
      // Removido o footer com botão "Ver mais"
    );
  }

  /// Constrói a seção de cartões
  Widget _buildCardsSection() {
    // Se não há dados, não exibe a seção
    if (_dashboardData == null) return const SizedBox.shrink();

    final cartoes = _dashboardData!.cartoesAtivos;

    // Se não há cartões, mostra seção vazia
    if (cartoes.isEmpty) {
      return _buildEmptySection(
        'Meus Cartões',
        'Nenhum cartão cadastrado',
        'Adicione um cartão para começar a gerenciar suas finanças',
        Icons.credit_card_outlined,
      );
    }

    // Cria lista de cards incluindo o card de ação
    final List<Widget> cardWidgets = cartoes
        .take(3)
        .map<Widget>(
          (cartao) => CreditCardCard(
            cardName: cartao.nomeCartao,
            lastFourDigits: cartao.ultimosDigitos,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => CardDetailsScreen(
                    card: {
                      'id': cartao.id,
                      'nomeCartao': cartao.nomeCartao,
                      'ultimosDigitos': cartao.ultimosDigitos,
                      'bandeira': cartao.bandeira,
                      'ativo': cartao.ativo,
                      'apelido': '',
                      'limite': 5000.0,
                      'limiteUtilizado': 1200.0,
                      'diaFechamento': 15,
                      'diaVencimento': 10,
                    },
                  ),
                ),
              );
            },
          ),
        )
        .toList();

    // Adiciona o card de ação no final se há espaço
    if (cartoes.length < 3) {
      cardWidgets.add(
        AddCreditCardCard(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const AddCardScreen()),
            );
          },
        ),
      );
    }

    return DashboardSection(
      title: 'Meus Cartões',
      onSeeAll: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const CardsListScreen()),
        );
      },
      content: HorizontalCardList(height: 140, cards: cardWidgets),
      // Removido o footer com botão "Ver mais"
    );
  }

  /// Constrói a seção de lançamentos
  Widget _buildTransactionsSection() {
    // Se não há dados, não exibe a seção
    if (_dashboardData == null) return const SizedBox.shrink();

    final lancamentos = _dashboardData!.ultimosLancamentos.take(4).toList();

    // Se não há lançamentos, mostra seção vazia
    if (lancamentos.isEmpty) {
      return _buildEmptySection(
        'Últimos Lançamentos',
        'Nenhum lançamento encontrado',
        'Adicione receitas e despesas para acompanhar seu fluxo financeiro',
        Icons.receipt_long_outlined,
      );
    }

    // Converte para o formato esperado pelos widgets
    final transactions = lancamentos
        .map(
          (lancamento) => {
            'title': lancamento.descricao,
            'amount': lancamento.valor,
            'category': lancamento.categoria,
            'date': lancamento.data,
            'isExpense': lancamento.isDespesa,
          },
        )
        .toList();

    return DashboardSection(
      title: 'Últimos Lançamentos',
      onSeeAll: () => NavigationHelper.goToTransactions(context),
      content: Column(
        children: transactions
            .take(3)
            .map(
              (transaction) => TransactionCard(
                title: transaction['title'] as String,
                amount: transaction['amount'] as double,
                category: transaction['category'] as String,
                date: transaction['date'] as DateTime,
                isExpense: transaction['isExpense'] as bool,
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) =>
                          TransactionDetailsScreen(transaction: transaction),
                    ),
                  );
                },
              ),
            )
            .toList(),
      ),
      footer: transactions.length > 3
          ? SeeMoreButton(
              onPressed: () => NavigationHelper.goToTransactions(context),
            )
          : null,
    );
  }

  /// Constrói a seção de metas financeiras
  Widget _buildGoalsSection() {
    // Se não há metas, exibe botão para criar
    if (_goals.isEmpty) {
      return _buildEmptyGoalsSection();
    }

    // Usa dados reais das metas
    final goalsToShow = _goals.take(3).toList();

    return DashboardSection(
      title: 'Metas Financeiras',
      onSeeAll: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const GoalsListScreen()),
        );
      },
      content: SizedBox(
        height: 120,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 4),
          itemCount: goalsToShow.length,
          itemBuilder: (context, index) {
            final goal = goalsToShow[index];
            return Container(
              width: 200,
              margin: const EdgeInsets.only(right: 12),
              child: Card(
                color: const Color(0xFF16213E),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(
                    color: Color(goal.colorValue).withValues(alpha: 0.3),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(6),
                            decoration: BoxDecoration(
                              color: Color(
                                goal.colorValue,
                              ).withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Icon(
                              Icons.flag,
                              color: Color(goal.colorValue),
                              size: 16,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              goal.titulo,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: goal.progresso,
                        backgroundColor: Colors.white24,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Color(goal.colorValue),
                        ),
                        minHeight: 6,
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            goal.valorAtualFormatado,
                            style: TextStyle(
                              color: Color(goal.colorValue),
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            goal.valorMetaFormatado,
                            style: const TextStyle(
                              color: Colors.white54,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// Constrói a seção vazia de metas financeiras
  Widget _buildEmptyGoalsSection() {
    return _buildEmptySection(
      'Metas Financeiras',
      'Nenhuma meta criada',
      'Defina objetivos financeiros para acompanhar seu progresso',
      Icons.flag_outlined,
    );
  }

  /// Constrói a seção de categorias
  Widget _buildCategoriesSection() {
    // Se não há categorias, exibe botão para criar
    if (_categories.isEmpty) {
      return _buildEmptyCategoriesSection();
    }

    // Usa dados reais das categorias
    final categoriesToShow = _categories.take(4).toList();

    return DashboardSection(
      title: 'Categorias',
      onSeeAll: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const CategoriesListScreen()),
        );
      },
      content: SizedBox(
        height: 100,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 4),
          itemCount: categoriesToShow.length,
          itemBuilder: (context, index) {
            final category = categoriesToShow[index];
            return Container(
              width: 120,
              margin: const EdgeInsets.only(right: 12),
              child: Card(
                color: const Color(0xFF16213E),
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(
                    color: category.colorValue.withValues(alpha: 0.3),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        category.iconData,
                        color: category.colorValue,
                        size: 20,
                      ),
                      const SizedBox(height: 4),
                      Flexible(
                        child: Text(
                          category.nome,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 11,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Flexible(
                        child: Text(
                          '${category.numeroTransacoes} transações',
                          style: const TextStyle(
                            color: Colors.white54,
                            fontSize: 9,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// Constrói a seção vazia de categorias
  Widget _buildEmptyCategoriesSection() {
    return _buildEmptySection(
      'Categorias',
      'Nenhuma categoria criada',
      'Organize suas finanças criando categorias para receitas e despesas',
      Icons.category_outlined,
    );
  }

  /// Constrói uma seção vazia com mensagem informativa
  Widget _buildEmptySection(
    String title,
    String message,
    String subtitle,
    IconData icon,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF4ECDC4).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Icon(icon, color: const Color(0xFF4ECDC4), size: 24),
            ],
          ),
          const SizedBox(height: 20),
          Icon(icon, color: Colors.grey[600], size: 48),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(color: Colors.grey[400], fontSize: 14),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          _buildActionButton(title),
        ],
      ),
    );
  }

  /// Constrói o botão de ação baseado no título da seção
  Widget _buildActionButton(String title) {
    String buttonText;
    VoidCallback onPressed;

    switch (title) {
      case 'Contas':
        buttonText = 'Adicionar Conta';
        onPressed = () {
          NavigationHelper.goToAccounts(context);
        };
        break;
      case 'Meus Cartões':
        buttonText = 'Adicionar Cartão';
        onPressed = () {
          NavigationHelper.goToCards(context);
        };
        break;
      case 'Últimos Lançamentos':
        buttonText = 'Adicionar Lançamento';
        onPressed = () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddTransactionScreen(),
            ),
          );
        };
        break;
      case 'Metas Financeiras':
        buttonText = 'Criar Meta';
        onPressed = () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const GoalsListScreen()),
          );
        };
        break;
      case 'Categorias':
        buttonText = 'Criar Categoria';
        onPressed = () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const CategoriesListScreen(),
            ),
          );
        };
        break;
      default:
        buttonText = 'Adicionar';
        onPressed = () {};
    }

    return ElevatedButton.icon(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.goldColor,
        foregroundColor: AppTheme.navyBlueColor,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      icon: const Icon(Icons.add, size: 18),
      label: Text(
        buttonText,
        style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
      ),
    );
  }
}
