using Shared.Enums;
using System.ComponentModel.DataAnnotations;

namespace Shared.ViewModels.Client
{
    /// <summary>
    /// ViewModel para filtrar metas financeiras
    /// </summary>
    public class MetaFilterViewModel
    {
        /// <summary>
        /// Status da meta para filtro
        /// </summary>
        public MetaStatus? Status { get; set; }

        /// <summary>
        /// Data de início para filtro por período de criação
        /// </summary>
        [DataType(DataType.Date)]
        public DateTime? DataInicio { get; set; }

        /// <summary>
        /// Data de fim para filtro por período de criação
        /// </summary>
        [DataType(DataType.Date)]
        public DateTime? DataFim { get; set; }

        /// <summary>
        /// Data de início para filtro por período de vencimento
        /// </summary>
        [DataType(DataType.Date)]
        public DateTime? DataVencimentoInicio { get; set; }

        /// <summary>
        /// Data de fim para filtro por período de vencimento
        /// </summary>
        [DataType(DataType.Date)]
        public DateTime? DataVencimentoFim { get; set; }

        /// <summary>
        /// IDs das categorias para filtro (permite múltiplas categorias)
        /// </summary>
        public List<string>? CategoriasIds { get; set; }

        /// <summary>
        /// Filtrar apenas metas ativas (FlgAtivo = true)
        /// </summary>
        public bool? ApenasAtivas { get; set; }

        /// <summary>
        /// Filtrar apenas metas mensais
        /// </summary>
        public bool? ApenasMensais { get; set; }

        /// <summary>
        /// Valor mínimo do alvo para filtro
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "O valor mínimo deve ser maior ou igual a zero")]
        public decimal? ValorAlvoMinimo { get; set; }

        /// <summary>
        /// Valor máximo do alvo para filtro
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "O valor máximo deve ser maior ou igual a zero")]
        public decimal? ValorAlvoMaximo { get; set; }

        /// <summary>
        /// Percentual mínimo de progresso (0-100)
        /// </summary>
        [Range(0, 100, ErrorMessage = "O percentual deve estar entre 0 e 100")]
        public decimal? ProgressoMinimo { get; set; }

        /// <summary>
        /// Percentual máximo de progresso (0-100)
        /// </summary>
        [Range(0, 100, ErrorMessage = "O percentual deve estar entre 0 e 100")]
        public decimal? ProgressoMaximo { get; set; }

        /// <summary>
        /// Busca por texto no nome ou descrição da meta
        /// </summary>
        [StringLength(100)]
        public string? TextoBusca { get; set; }

        /// <summary>
        /// Ordenação dos resultados
        /// </summary>
        public MetaOrdenacao? Ordenacao { get; set; }

        /// <summary>
        /// Direção da ordenação (crescente/decrescente)
        /// </summary>
        public bool OrdenacaoDecrescente { get; set; } = false;

        /// <summary>
        /// Valida se as datas estão em ordem correta
        /// </summary>
        public bool IsValid()
        {
            // Validar período de criação
            if (DataInicio.HasValue && DataFim.HasValue && DataInicio > DataFim)
                return false;

            // Validar período de vencimento
            if (DataVencimentoInicio.HasValue && DataVencimentoFim.HasValue && DataVencimentoInicio > DataVencimentoFim)
                return false;

            // Validar valores do alvo
            if (ValorAlvoMinimo.HasValue && ValorAlvoMaximo.HasValue && ValorAlvoMinimo > ValorAlvoMaximo)
                return false;

            // Validar percentuais de progresso
            if (ProgressoMinimo.HasValue && ProgressoMaximo.HasValue && ProgressoMinimo > ProgressoMaximo)
                return false;

            return true;
        }

        /// <summary>
        /// Retorna uma mensagem de erro de validação se houver
        /// </summary>
        public string? GetValidationError()
        {
            if (DataInicio.HasValue && DataFim.HasValue && DataInicio > DataFim)
                return "Data de início não pode ser maior que data de fim";

            if (DataVencimentoInicio.HasValue && DataVencimentoFim.HasValue && DataVencimentoInicio > DataVencimentoFim)
                return "Data de vencimento inicial não pode ser maior que data de vencimento final";

            if (ValorAlvoMinimo.HasValue && ValorAlvoMaximo.HasValue && ValorAlvoMinimo > ValorAlvoMaximo)
                return "Valor alvo mínimo não pode ser maior que valor alvo máximo";

            if (ProgressoMinimo.HasValue && ProgressoMaximo.HasValue && ProgressoMinimo > ProgressoMaximo)
                return "Progresso mínimo não pode ser maior que progresso máximo";

            return null;
        }
    }

    /// <summary>
    /// Opções de ordenação para metas
    /// </summary>
    public enum MetaOrdenacao
    {
        Nome = 1,
        DataCriacao = 2,
        DataVencimento = 3,
        ValorAlvo = 4,
        ProgressoAtual = 5,
        PercentualProgresso = 6,
        Status = 7
    }
}
