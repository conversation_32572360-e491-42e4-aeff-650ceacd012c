[{"ContainingType": "CasteloForte.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginRequest", "Type": "Shared.ViewModels.Client.LoginRequestViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.AuthController", "Method": "Logout", "RelativePath": "api/Auth/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "logoutRequest", "Type": "Shared.ViewModels.Client.LogoutRequestViewModel", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.AuthController", "Method": "GetCurrentUser", "RelativePath": "api/Auth/me", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "refreshRequest", "Type": "Shared.ViewModels.Client.RefreshTokenRequestViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.AuthController", "Method": "GetAuthStatus", "RelativePath": "api/Auth/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.AuthController", "Method": "ValidateToken", "RelativePath": "api/Auth/validate-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "validateRequest", "Type": "Shared.ViewModels.Client.ValidateTokenRequestViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.AuthAdminController", "Method": "CheckAdminStatus", "RelativePath": "api/AuthAdmin/check-admin", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.AuthAdminController", "Method": "Login<PERSON><PERSON><PERSON>", "RelativePath": "api/AuthAdmin/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginRequest", "Type": "Shared.ViewModels.Admin.LoginAdminRequestViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.AuthAdminController", "Method": "LogoutAdmin", "RelativePath": "api/AuthAdmin/logout", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.AuthAdminController", "Method": "ValidateAdminToken", "RelativePath": "api/AuthAdmin/validate-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Shared.ViewModels.Admin.ValidateAdminTokenRequestViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.CartaoController", "Method": "BuscarTodos", "RelativePath": "api/Cartao", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.CartaoController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "api/Cartao", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "cartaoViewModel", "Type": "Shared.ViewModels.Client.CartaoViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.CartaoController", "Method": "BuscarPorId", "RelativePath": "api/<PERSON>tao/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.CartaoController", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/<PERSON>tao/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "cartaoViewModel", "Type": "Shared.ViewModels.Client.CartaoViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.CartaoController", "Method": "Inativar", "RelativePath": "api/<PERSON>tao/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.CartaoController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "api/<PERSON>tao/{id}/reativar", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.CartaoController", "Method": "BuscarContagem", "RelativePath": "api/Cartao/contagem", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.CartaoController", "Method": "BuscarPorApelido", "RelativePath": "api/Cartao/filtrar/apelido/{apelido}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "apelido", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.CartaoController", "Method": "BuscarPorNome", "RelativePath": "api/Cartao/filtrar/nome/{nome}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "nome", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.CartaoController", "Method": "BuscarPaginado", "RelativePath": "api/Cartao/paginado", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.CategoriaController", "Method": "BuscarTodos", "RelativePath": "api/Categoria", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ativa", "Type": "System.Boolean", "IsRequired": false}, {"Name": "tipo", "Type": "System.String", "IsRequired": false}, {"Name": "search", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.ViewModels.Client.CategoriaViewModel, Shared, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "CasteloForte.Controllers.CategoriaController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "api/Categoria", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "viewModel", "Type": "Shared.ViewModels.Client.CategoriaCreateUpdateViewModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.ViewModels.Client.CategoriaViewModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "CasteloForte.Controllers.CategoriaController", "Method": "BuscarPorId", "RelativePath": "api/Categoria/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.ViewModels.Client.CategoriaViewModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "CasteloForte.Controllers.CategoriaController", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Categoria/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "viewModel", "Type": "Shared.ViewModels.Client.CategoriaCreateUpdateViewModel", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.ViewModels.Client.CategoriaViewModel", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "CasteloForte.Controllers.CategoriaController", "Method": "BuscarParaSelect", "RelativePath": "api/Categoria/select", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "ativa", "Type": "System.Boolean", "IsRequired": false}, {"Name": "tipo", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}, {"ContainingType": "CasteloForte.Controllers.ContaController", "Method": "BuscarTodas", "RelativePath": "api/Conta", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.ContaController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "api/Conta", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "conta", "Type": "Shared.ViewModels.Client.ContaViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.ContaController", "Method": "BuscarPorId", "RelativePath": "api/Conta/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.ContaController", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Conta/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "conta", "Type": "Shared.ViewModels.Client.ContaViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.ContaController", "Method": "Inativar", "RelativePath": "api/Conta/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.ContaController", "Method": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/Conta/{id}/saldo", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "novoSaldo", "Type": "System.Decimal", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.ContaController", "Method": "BuscarPorNome", "RelativePath": "api/Conta/nome/{nome}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "nome", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.ContaController", "Method": "CalcularSaldoTotal", "RelativePath": "api/Conta/saldo-total", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.ContaController", "Method": "BuscarPorTipo", "RelativePath": "api/Conta/tipo/{tipo}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "tipo", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.DashboardController", "Method": "GetDashboardData", "RelativePath": "api/Dashboard/dados", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Shared.ViewModels.Client.DashboardResponseViewModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "CasteloForte.Controllers.DashboardController", "Method": "GetDiagnostico", "RelativePath": "api/Dashboard/diagnostico", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "CasteloForte.Controllers.DashboardController", "Method": "GetDiagnosticoCriptografia", "RelativePath": "api/Dashboard/diagnostico-criptografia", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "CasteloForte.Controllers.DashboardController", "Method": "UpdateQuestionarioPerfil", "RelativePath": "api/Dashboard/questionario-perfil", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "CasteloForte.Controllers.UpdateQuestionarioPerfilRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Object", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "CasteloForteAdmin.Controllers.HistoricoAdminController", "Method": "BuscarPorAcao", "RelativePath": "api/HistoricoAdmin/acao/{acao}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "acao", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.HistoricoAdminController", "Method": "ObterEstatisticas", "RelativePath": "api/HistoricoAdmin/estatisticas", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.HistoricoAdminController", "Method": "BuscarHistoricoFiltrado", "RelativePath": "api/HistoricoAdmin/filtrados", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "filtro", "Type": "Shared.ViewModels.Admin.FiltroHistoricoViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.HistoricoAdminController", "Method": "BuscarPorPeriodo", "RelativePath": "api/HistoricoAdmin/periodo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "dataInicio", "Type": "System.DateTime", "IsRequired": false}, {"Name": "dataFim", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.HistoricoAdminController", "Method": "BuscarTodos", "RelativePath": "api/HistoricoAdmin/todos", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.HistoricoAdminController", "Method": "BuscarPorUsuario", "RelativePath": "api/HistoricoAdmin/usuario/{idUsuario}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "idUsuario", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.LogErroAdminController", "Method": "BuscarPorController", "RelativePath": "api/LogErroAdmin/controller/{controller}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "controller", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.LogErroAdminController", "Method": "ObterEstatisticas", "RelativePath": "api/LogErroAdmin/estatisticas", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.LogErroAdminController", "Method": "BuscarLogsFiltrados", "RelativePath": "api/LogErroAdmin/filtrados", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "filtro", "Type": "Shared.ViewModels.Admin.FiltroLogErroViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.LogErroAdminController", "Method": "BuscarPorPeriodo", "RelativePath": "api/LogErroAdmin/periodo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "dataInicio", "Type": "System.DateTime", "IsRequired": false}, {"Name": "dataFim", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.LogErroAdminController", "Method": "BuscarTodos", "RelativePath": "api/LogErroAdmin/todos", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.LogErroAdminController", "Method": "BuscarPorUsuario", "RelativePath": "api/LogErroAdmin/usuario/{idUsuario}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "idUsuario", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.MetaController", "Method": "GetAll", "RelativePath": "api/Meta", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "DataInicio", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DataFim", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DataVencimentoInicio", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "DataVencimentoFim", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "CategoriasIds", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "<PERSON><PERSON>as<PERSON><PERSON><PERSON>", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ApenasMensais", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ValorAlvoMinimo", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ValorAlvoMaximo", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ProgressoMinimo", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ProgressoMaximo", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "TextoBusca", "Type": "System.String", "IsRequired": false}, {"Name": "Ordenacao", "Type": "System.String", "IsRequired": false}, {"Name": "OrdenacaoDecrescente", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.MetaController", "Method": "CreateOrUpdate", "RelativePath": "api/Meta", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "meta", "Type": "Shared.ViewModels.Client.MetaViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.MetaController", "Method": "CreateOrUpdate", "RelativePath": "api/Meta", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "meta", "Type": "Shared.ViewModels.Client.MetaViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.MetaController", "Method": "GetById", "RelativePath": "api/Meta/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.MetaController", "Method": "Delete", "RelativePath": "api/Meta/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.MetaController", "Method": "GetFilterOptions", "RelativePath": "api/Meta/filter-options", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.MigrationController", "Method": "AddAdminFlagToUsers", "RelativePath": "api/Migration/add-admin-flag", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "adminEmails", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.MigrationController", "Method": "CheckAdminStatus", "RelativePath": "api/Migration/check-admin-status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.MigrationController", "Method": "PromoteToAdmin", "RelativePath": "api/Migration/promote-to-admin/{usuarioId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "usuarioId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.PlanoFinanceiroController", "Method": "BuscarTodos", "RelativePath": "api/PlanoFinanceiro", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.PlanoFinanceiroController", "Method": "<PERSON><PERSON><PERSON>", "RelativePath": "api/PlanoFinanceiro", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "plano", "Type": "Shared.ViewModels.Client.PlanoFinanceiroViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.PlanoFinanceiroController", "Method": "BuscarPorId", "RelativePath": "api/PlanoFinanceiro/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.PlanoFinanceiroController", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/PlanoFinanceiro/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "plano", "Type": "Shared.ViewModels.Client.PlanoFinanceiroViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.PlanoFinanceiroController", "Method": "Inativar", "RelativePath": "api/PlanoFinanceiro/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.PlanoFinanceiroController", "Method": "<PERSON><PERSON><PERSON><PERSON>", "RelativePath": "api/PlanoFinanceiro/{id}/arquivar", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.PlanoFinanceiroController", "Method": "Duplicar", "RelativePath": "api/PlanoFinanceiro/{id}/duplicar", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "novoTitulo", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.PlanoFinanceiroController", "Method": "Restaurar", "RelativePath": "api/PlanoFinanceiro/{id}/restaurar", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.PlanoFinanceiroController", "Method": "BuscarPorPeriodoCriacao", "RelativePath": "api/PlanoFinanceiro/periodo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "dataInicio", "Type": "System.DateTime", "IsRequired": false}, {"Name": "dataFim", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "CasteloForte.Controllers.PlanoFinanceiroController", "Method": "BuscarPorTitulo", "RelativePath": "api/PlanoFinanceiro/titulo/{titulo}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "titulo", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.UsuarioAdminController", "Method": "BuscarPorId", "RelativePath": "api/UsuarioAdmin/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.UsuarioAdminController", "Method": "ObterEstatisticasUsuarios", "RelativePath": "api/UsuarioAdmin/estatisticas", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.UsuarioAdminController", "Method": "BuscarUsuariosFiltrados", "RelativePath": "api/UsuarioAdmin/filtrados", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "filtro", "Type": "Shared.ViewModels.Admin.FiltroUsuarioViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.UsuarioPublicoController", "Method": "CadastrarUsuario", "RelativePath": "api/UsuarioPublico/cadastrar", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Shared.ViewModels.Admin.CadastroUsuarioViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "CasteloForteAdmin.Controllers.UsuarioPublicoController", "Method": "CadastroUsuarioLegacy", "RelativePath": "api/UsuarioPublico/legacy", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "model", "Type": "Shared.ViewModels.Admin.UsuarioViewModel", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_7", "RelativePath": "health/mongodb", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}]