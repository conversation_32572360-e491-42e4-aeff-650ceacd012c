import 'package:flutter/material.dart';
import 'create_goal_screen.dart';
import 'goal_details_screen.dart';
import '../../../core/utils/navigation_helper.dart';
import '../../../core/services/logger_service.dart';
import '../data/goals_service.dart';
import '../models/meta_filter_model.dart';
import '../models/meta_response_models.dart';

class GoalsListScreen extends StatefulWidget {
  const GoalsListScreen({super.key});

  @override
  State<GoalsListScreen> createState() => _GoalsListScreenState();
}

class _GoalsListScreenState extends State<GoalsListScreen> {
  List<MetaViewModel> _metas = [];
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadMetas();
  }

  Future<void> _loadMetas() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      LoggerService.info('Carregando metas ativas...');

      // Criar filtro para buscar apenas metas ativas
      final filtros = MetaFilterModel(apenasAtivas: true);

      final response = await GoalsService.getAllMetas(filtros: filtros);

      setState(() {
        _metas = response.metas;
        _isLoading = false;
      });

      LoggerService.info('${_metas.length} metas carregadas com sucesso');
    } catch (e) {
      LoggerService.failure('Erro ao carregar metas: $e');
      setState(() {
        _errorMessage = 'Erro ao carregar metas. Tente novamente.';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => NavigationHelper.safeGoBack(context),
        ),
        title: const Text(
          'Minhas Metas',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4CAF50)),
              ),
            )
          : _errorMessage != null
          ? _buildErrorState()
          : _metas.isEmpty
          ? _buildEmptyState()
          : ListView.builder(
              padding: const EdgeInsets.all(20),
              itemCount: _metas.length,
              itemBuilder: (context, index) {
                final meta = _metas[index];
                return _buildMetaCard(meta);
              },
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToCreateGoal,
        backgroundColor: const Color(0xFF4CAF50),
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.flag_outlined,
            size: 80,
            color: Colors.white.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 20),
          Text(
            'Nenhuma meta cadastrada',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Crie sua primeira meta financeira',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.5),
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 30),
          ElevatedButton.icon(
            onPressed: _navigateToCreateGoal,
            icon: const Icon(Icons.add, color: Colors.white),
            label: const Text(
              'Criar Meta',
              style: TextStyle(color: Colors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 80, color: Colors.red[400]),
          const SizedBox(height: 20),
          Text(
            _errorMessage ?? 'Erro desconhecido',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 30),
          ElevatedButton.icon(
            onPressed: _loadMetas,
            icon: const Icon(Icons.refresh, color: Colors.white),
            label: const Text(
              'Tentar Novamente',
              style: TextStyle(color: Colors.white),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF4CAF50),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetaCard(MetaViewModel meta) {
    final progress = meta.progresso;
    final progressPercentage = (progress * 100).toInt();
    final metaColor = meta.isVencida ? Colors.red : const Color(0xFF4CAF50);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _navigateToGoalDetails(meta),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: metaColor.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: metaColor.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        meta.isConcluida
                            ? Icons.check_circle
                            : Icons.track_changes,
                        color: metaColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            meta.nome,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${meta.valorAtualFormatado} de ${meta.valorMetaFormatado}',
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontSize: 14,
                            ),
                          ),
                          if (meta.isVencida) ...[
                            const SizedBox(height: 2),
                            Text(
                              'Meta vencida',
                              style: TextStyle(
                                color: Colors.red[400],
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '$progressPercentage%',
                          style: TextStyle(
                            color: metaColor,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (meta.diasRestantes > 0) ...[
                          const SizedBox(height: 2),
                          Text(
                            '${meta.diasRestantes} dias',
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.5),
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: LinearProgressIndicator(
                    value: progress,
                    backgroundColor: Colors.white.withValues(alpha: 0.1),
                    valueColor: AlwaysStoppedAnimation<Color>(metaColor),
                    minHeight: 8,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildActionButton(
                      icon: Icons.edit,
                      label: 'Editar',
                      onPressed: () => _navigateToEditMeta(meta),
                    ),
                    _buildActionButton(
                      icon: Icons.delete,
                      label: 'Excluir',
                      color: Colors.red[400],
                      onPressed: () => _showDeleteConfirmation(meta),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    Color? color,
  }) {
    final buttonColor = color ?? Colors.white.withValues(alpha: 0.7);

    return TextButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 16, color: buttonColor),
      label: Text(label, style: TextStyle(color: buttonColor, fontSize: 12)),
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
    );
  }

  void _navigateToGoalDetails(MetaViewModel meta) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GoalDetailsScreen(goal: meta.toJson()),
      ),
    ).then((_) {
      // Recarregar a lista quando voltar da tela de detalhes
      _loadMetas();
    });
  }

  void _navigateToEditMeta(MetaViewModel meta) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            CreateGoalScreen(isEditing: true, existingGoal: meta.toJson()),
      ),
    );

    if (result != null) {
      // Recarregar a lista após edição
      _loadMetas();
    }
  }

  void _navigateToCreateGoal() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CreateGoalScreen()),
    );

    if (result != null) {
      // Recarregar a lista após criação
      _loadMetas();
    }
  }

  void _showDeleteConfirmation(MetaViewModel meta) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF16213E),
          title: const Text(
            'Confirmar Exclusão',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            'Tem certeza que deseja excluir a meta "${meta.nome}"?\n\nEsta ação não pode ser desfeita.',
            style: TextStyle(color: Colors.white.withValues(alpha: 0.8)),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancelar',
                style: TextStyle(color: Colors.white.withValues(alpha: 0.7)),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteMeta(meta);
              },
              child: Text('Excluir', style: TextStyle(color: Colors.red[400])),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteMeta(MetaViewModel meta) async {
    try {
      LoggerService.info('Excluindo meta: ${meta.nome}');

      await GoalsService.deleteMeta(meta.id!);

      LoggerService.info('Meta excluída com sucesso');

      // Recarregar a lista
      _loadMetas();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Meta "${meta.nome}" excluída com sucesso'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      LoggerService.failure('Erro ao excluir meta: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao excluir meta: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
