import 'package:flutter/material.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/widgets/loading_widget.dart';
import '../../../core/widgets/error_widget.dart';
import '../data/accounts_service.dart';
import '../data/models/account_model.dart';
import 'edit_account_screen.dart';

/// Wrapper que carrega uma conta por ID e exibe a tela de edição
class AccountFormWrapper extends StatefulWidget {
  final String accountId;

  const AccountFormWrapper({
    super.key,
    required this.accountId,
  });

  @override
  State<AccountFormWrapper> createState() => _AccountFormWrapperState();
}

class _AccountFormWrapperState extends State<AccountFormWrapper> {
  AccountModel? _account;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadAccount();
  }

  Future<void> _loadAccount() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final account = await AccountsService.getAccountById(widget.accountId);
      
      setState(() {
        _account = account;
        _isLoading = false;
        if (account == null) {
          _errorMessage = 'Conta não encontrada';
        }
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Erro ao carregar conta: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          title: const Text('Carregando...'),
          backgroundColor: AppColors.background,
          foregroundColor: Colors.white,
        ),
        body: const LoadingWidget(),
      );
    }

    if (_errorMessage != null) {
      return Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          title: const Text('Erro'),
          backgroundColor: AppColors.background,
          foregroundColor: Colors.white,
        ),
        body: CustomErrorWidget(
          message: _errorMessage!,
          onRetry: _loadAccount,
        ),
      );
    }

    if (_account == null) {
      return Scaffold(
        backgroundColor: AppColors.background,
        appBar: AppBar(
          title: const Text('Conta não encontrada'),
          backgroundColor: AppColors.background,
          foregroundColor: Colors.white,
        ),
        body: const Center(
          child: Text(
            'Conta não encontrada',
            style: TextStyle(color: Colors.white70),
          ),
        ),
      );
    }

    // Converte AccountModel para Map para compatibilidade com EditAccountScreen
    final accountMap = {
      'id': _account!.id,
      'nomeBanco': _account!.nome,
      'tipoConta': _account!.tipoConta,
      'saldo': _account!.saldo,
      'ativa': _account!.ativa,
      'apelido': _account!.apelidoConta,
      'dataCriacao': _account!.dtaCadastro?.toIso8601String(),
      'dataUltimaAtualizacao': _account!.dtaAlteracao?.toIso8601String(),
    };

    return EditAccountScreen(account: accountMap);
  }
}
