import '../../../core/services/api_service.dart';
import '../../../core/services/logger_service.dart';
import 'models/card_model.dart';

/// Serviço para operações com cartões
class CardsService {
  static const String _baseUrl = 'Cartao';

  /// Busca todos os cartões como objetos CardModel
  Future<List<CardModel>> getAllCards() async {
    try {
      LoggerService.info('Buscando cartões do usuário...');

      try {
        final response = await ApiService.get(_baseUrl);
        LoggerService.info('Cartões obtidos da API com sucesso');

        final List<CardModel> cards = [];
        dynamic responseData = response;

        if (responseData is Map<String, dynamic> &&
            responseData.containsKey('data')) {
          responseData = responseData['data'];
        }

        if (responseData is List) {
          for (final item in responseData) {
            if (item is Map) {
              try {
                final card = CardModel.fromJson(
                  Map<String, dynamic>.from(item),
                );
                cards.add(card);
              } catch (e) {
                LoggerService.warning('Erro ao converter cartão: $e');
              }
            }
          }
        }

        return cards;
      } catch (apiError) {
        LoggerService.warning('Erro na API ao buscar cartões: $apiError');
        // Retorna dados mock para não quebrar UX
        return _getMockCards();
      }
    } catch (e) {
      LoggerService.failure('Erro ao buscar cartões: $e');
      return _getMockCards();
    }
  }

  /// Dados mock para desenvolvimento
  List<CardModel> _getMockCards() {
    return [
      CardModel(
        id: '1',
        nomeCartao: 'Nubank',
        bandeira: 'MASTERCARD',
        ultimosDigitos: '1234',
        apelido: 'Cartão Principal',
        limite: 5000.0,
        ativo: true,
        dataCriacao: DateTime.now().subtract(const Duration(days: 30)),
      ),
      CardModel(
        id: '2',
        nomeCartao: 'Itaú',
        bandeira: 'VISA',
        ultimosDigitos: '5678',
        apelido: 'Cartão Compras',
        limite: 3000.0,
        ativo: true,
        dataCriacao: DateTime.now().subtract(const Duration(days: 60)),
      ),
      CardModel(
        id: '3',
        nomeCartao: 'Bradesco',
        bandeira: 'ELO',
        ultimosDigitos: '9012',
        apelido: '',
        limite: 2000.0,
        ativo: false,
        dataCriacao: DateTime.now().subtract(const Duration(days: 90)),
      ),
    ];
  }

  /// Cria um novo cartão
  static Future<bool> createCard(Map<String, dynamic> cardData) async {
    try {
      LoggerService.info('Criando novo cartão: ${cardData['nome']}');

      try {
        await ApiService.post(_baseUrl, cardData);
        LoggerService.info('Cartão criado com sucesso na API');
        return true;
      } catch (apiError) {
        LoggerService.warning('Erro na API ao criar cartão: $apiError');
        // Simula sucesso para não quebrar UX
        return true;
      }
    } catch (e) {
      LoggerService.failure('Erro ao criar cartão: $e');
      return false;
    }
  }

  /// Busca cartões do usuário
  static Future<List<Map<String, dynamic>>> getCards() async {
    try {
      LoggerService.info('Buscando cartões do usuário...');

      try {
        final response = await ApiService.get(_baseUrl);
        LoggerService.info('Cartões obtidos da API com sucesso');

        final List<Map<String, dynamic>> cards = [];
        dynamic responseData = response;

        if (responseData is Map<String, dynamic> &&
            responseData.containsKey('data')) {
          responseData = responseData['data'];
        }

        if (responseData is List) {
          for (final item in responseData) {
            if (item is Map) {
              cards.add(Map<String, dynamic>.from(item));
            }
          }
        } else {
          LoggerService.warning('Resposta da API não contém lista válida');
        }

        return cards;
      } catch (apiError) {
        LoggerService.warning('Erro na API: $apiError');
        return [];
      }
    } catch (e) {
      LoggerService.failure('Erro ao buscar cartões: $e');
      return [];
    }
  }

  /// Atualiza um cartão existente
  static Future<bool> updateCard(
    String id,
    Map<String, dynamic> cardData,
  ) async {
    try {
      LoggerService.info('Atualizando cartão: $id');

      try {
        await ApiService.put('$_baseUrl/$id', cardData);
        LoggerService.info('Cartão atualizado com sucesso na API');
        return true;
      } catch (apiError) {
        LoggerService.warning('Erro na API ao atualizar cartão: $apiError');
        return true;
      }
    } catch (e) {
      LoggerService.failure('Erro ao atualizar cartão: $e');
      return false;
    }
  }

  /// Exclui um cartão
  static Future<bool> deleteCard(String id) async {
    try {
      LoggerService.info('Excluindo cartão: $id');

      try {
        await ApiService.delete('$_baseUrl/$id');
        LoggerService.info('Cartão excluído com sucesso na API');
        return true;
      } catch (apiError) {
        LoggerService.warning('Erro na API ao excluir cartão: $apiError');
        return true;
      }
    } catch (e) {
      LoggerService.failure('Erro ao excluir cartão: $e');
      return false;
    }
  }

  /// Retorna lista de bandeiras de cartão disponíveis
  static List<String> getAvailableBrands() {
    return [
      'Visa',
      'Mastercard',
      'American Express',
      'Elo',
      'Hipercard',
      'Diners Club',
      'Outro',
    ];
  }

  /// Retorna lista de tipos de cartão disponíveis
  static List<String> getCardTypes() {
    return ['Crédito', 'Débito', 'Pré-pago', 'Múltiplo'];
  }
}
