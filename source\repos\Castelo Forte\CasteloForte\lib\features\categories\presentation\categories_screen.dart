import 'package:flutter/material.dart';
import '../models/categoria_model.dart';
import '../models/category_model.dart';
import '../data/categories_service.dart';
import '../../../core/widgets/safe_back_button.dart';
import '../../../core/utils/constants.dart';

class CategoriesScreen extends StatefulWidget {
  const CategoriesScreen({super.key});

  @override
  State<CategoriesScreen> createState() => _CategoriesScreenState();
}

class _CategoriesScreenState extends State<CategoriesScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  List<CategoryModel> _receitas = [];
  List<CategoryModel> _despesas = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _carregarCategorias();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _carregarCategorias() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final receitas = await CategoriesService.getCategoriesByType('RECEITA');
      final despesas = await CategoriesService.getCategoriesByType('DESPESA');

      setState(() {
        _receitas = receitas;
        _despesas = despesas;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: SafeBackButton(fallbackRoute: AppConstants.dashboardRoute),
        title: const Text(
          'Categorias',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add, color: Colors.white),
            onPressed: () => _mostrarDialogNovaCategoria(),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: const Color(0xFF4ECDC4),
          labelColor: Colors.white,
          unselectedLabelColor: Colors.grey,
          tabs: const [
            Tab(text: 'Receitas'),
            Tab(text: 'Despesas'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: Color(0xFF4ECDC4)),
            )
          : _error != null
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    'Erro ao carregar categorias',
                    style: TextStyle(
                      color: Colors.red[300],
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _error!,
                    style: const TextStyle(color: Colors.grey, fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _carregarCategorias,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4ECDC4),
                    ),
                    child: const Text('Tentar Novamente'),
                  ),
                ],
              ),
            )
          : TabBarView(
              controller: _tabController,
              children: [
                _buildCategoriasList(_receitas),
                _buildCategoriasList(_despesas),
              ],
            ),
    );
  }

  Widget _buildCategoriasList(List<CategoryModel> categorias) {
    if (categorias.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.category_outlined, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Nenhuma categoria encontrada',
              style: TextStyle(color: Colors.grey, fontSize: 18),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _carregarCategorias,
      color: const Color(0xFF4ECDC4),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: categorias.length,
        itemBuilder: (context, index) {
          final categoria = categorias[index];
          return _buildCategoriaCard(categoria);
        },
      ),
    );
  }

  Widget _buildCategoriaCard(CategoryModel categoria) {
    final cor = Color(categoria.cor);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      color: const Color(0xFF16213E),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: cor.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            IconData(categoria.icone, fontFamily: 'MaterialIcons'),
            color: cor,
            size: 24,
          ),
        ),
        title: Text(
          categoria.nome,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (categoria.descricao != null && categoria.descricao!.isNotEmpty)
              Text(
                categoria.descricao!,
                style: const TextStyle(color: Colors.grey, fontSize: 14),
              ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(Icons.receipt_long, size: 16, color: Colors.grey[400]),
                const SizedBox(width: 4),
                Text(
                  '${categoria.numeroTransacoes} transações',
                  style: TextStyle(color: Colors.grey[400], fontSize: 12),
                ),
                const SizedBox(width: 16),
                Icon(Icons.flag, size: 16, color: Colors.grey[400]),
                const SizedBox(width: 4),
                Text(
                  '${categoria.numeroMetas} metas',
                  style: TextStyle(color: Colors.grey[400], fontSize: 12),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.grey),
          onSelected: (value) => _handleMenuAction(value, categoria),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, color: Colors.blue),
                  SizedBox(width: 8),
                  Text('Editar'),
                ],
              ),
            ),
            if (categoria.ativa)
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Excluir'),
                  ],
                ),
              )
            else
              const PopupMenuItem(
                value: 'reactivate',
                child: Row(
                  children: [
                    Icon(Icons.restore, color: Colors.green),
                    SizedBox(width: 8),
                    Text('Reativar'),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(String action, CategoryModel categoria) {
    switch (action) {
      case 'edit':
        _mostrarDialogEditarCategoria(categoria);
        break;
      case 'delete':
        _confirmarExclusao(categoria);
        break;
      case 'reactivate':
        _reativarCategoria(categoria);
        break;
    }
  }

  void _mostrarDialogNovaCategoria() {
    showDialog(
      context: context,
      builder: (context) => _CategoriaDialog(
        onSave: (categoria) async {
          try {
            // Convert to CategoryFormModel and use new service
            // For now, just show success message
            _carregarCategorias();
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Categoria criada com sucesso!'),
                  backgroundColor: Color(0xFF4ECDC4),
                ),
              );
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Erro ao criar categoria: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
      ),
    );
  }

  void _mostrarDialogEditarCategoria(CategoryModel categoria) {
    // Convert CategoryModel to CategoriaModel for the dialog
    final categoriaDialog = CategoriaModel(
      id: categoria.id,
      nome: categoria.nome,
      descricao: categoria.descricao,
      tipo: categoria.tipo,
      cor: '#${categoria.cor.toRadixString(16).padLeft(8, '0').substring(2)}',
      icone: categoria.icone.toString(),
      ativa: categoria.ativa,
      dataCriacao: categoria.dataCriacao,
      ordem: categoria.ordem,
    );

    showDialog(
      context: context,
      builder: (context) => _CategoriaDialog(
        categoria: categoriaDialog,
        onSave: (categoriaAtualizada) async {
          try {
            // TODO: Implement update with new service
            _carregarCategorias();
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Categoria atualizada com sucesso!'),
                  backgroundColor: Color(0xFF4ECDC4),
                ),
              );
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Erro ao atualizar categoria: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
      ),
    );
  }

  void _confirmarExclusao(CategoryModel categoria) async {
    // Primeiro verifica se pode excluir
    bool podeExcluir = false;
    try {
      podeExcluir = await CategoriesService.canDeleteCategory(categoria.id);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao verificar categoria: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    if (!podeExcluir) {
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: const Color(0xFF16213E),
            title: const Text(
              'Não é possível excluir',
              style: TextStyle(color: Colors.white),
            ),
            content: Text(
              'A categoria "${categoria.nome}" não pode ser excluída pois possui transações ou metas associadas.',
              style: const TextStyle(color: Colors.grey),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text(
                  'OK',
                  style: TextStyle(color: Color(0xFF4ECDC4)),
                ),
              ),
            ],
          ),
        );
      }
      return;
    }

    // Mostra confirmação de exclusão
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: const Color(0xFF16213E),
          title: const Text(
            'Confirmar Exclusão',
            style: TextStyle(color: Colors.white),
          ),
          content: Text(
            'Tem certeza que deseja excluir a categoria "${categoria.nome}"?',
            style: const TextStyle(color: Colors.grey),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                'Cancelar',
                style: TextStyle(color: Colors.grey),
              ),
            ),
            TextButton(
              onPressed: () async {
                Navigator.pop(context);
                try {
                  await CategoriesService.deleteCategory(categoria.id);
                  _carregarCategorias();
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Categoria excluída com sucesso!'),
                        backgroundColor: Color(0xFF4ECDC4),
                      ),
                    );
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('Erro ao excluir categoria: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }
              },
              child: const Text('Excluir', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      );
    }
  }

  void _reativarCategoria(CategoryModel categoria) async {
    try {
      // TODO: Implement reactivate with new service
      // await CategoriesService.reactivateCategory(categoria.id ?? '');
      _carregarCategorias();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Categoria reativada com sucesso!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao reativar categoria: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

class _CategoriaDialog extends StatefulWidget {
  final CategoriaModel? categoria;
  final Function(CategoriaCreateUpdateModel) onSave;

  const _CategoriaDialog({this.categoria, required this.onSave});

  @override
  State<_CategoriaDialog> createState() => _CategoriaDialogState();
}

class _CategoriaDialogState extends State<_CategoriaDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nomeController = TextEditingController();
  final _descricaoController = TextEditingController();
  String _tipoSelecionado = 'Despesa';
  String? _corSelecionada;
  String? _iconeSelecionado;
  int _ordem = 0;

  final List<String> _tipos = ['Receita', 'Despesa'];
  final List<String> _cores = [
    '#4ECDC4',
    '#45B7D1',
    '#96CEB4',
    '#FFEAA7',
    '#DDA0DD',
    '#98D8C8',
    '#F7DC6F',
    '#BB8FCE',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.categoria != null) {
      _nomeController.text = widget.categoria!.nome;
      _descricaoController.text = widget.categoria!.descricao ?? '';
      _tipoSelecionado = widget.categoria!.tipo;
      _corSelecionada = widget.categoria!.cor;
      _iconeSelecionado = widget.categoria!.icone;
      _ordem = widget.categoria!.ordem;
    }
  }

  @override
  void dispose() {
    _nomeController.dispose();
    _descricaoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: const Color(0xFF16213E),
      title: Text(
        widget.categoria == null ? 'Nova Categoria' : 'Editar Categoria',
        style: const TextStyle(color: Colors.white),
      ),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Nome
                TextFormField(
                  controller: _nomeController,
                  style: const TextStyle(color: Colors.white),
                  decoration: const InputDecoration(
                    labelText: 'Nome *',
                    labelStyle: TextStyle(color: Colors.grey),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xFF4ECDC4)),
                    ),
                    errorBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.red),
                    ),
                    focusedErrorBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.red),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Nome é obrigatório';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Descrição
                TextFormField(
                  controller: _descricaoController,
                  style: const TextStyle(color: Colors.white),
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'Descrição',
                    labelStyle: TextStyle(color: Colors.grey),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xFF4ECDC4)),
                    ),
                  ),
                ),
                const SizedBox(height: 16),

                // Tipo
                DropdownButtonFormField<String>(
                  value: _tipoSelecionado,
                  style: const TextStyle(color: Colors.white),
                  dropdownColor: const Color(0xFF16213E),
                  decoration: const InputDecoration(
                    labelText: 'Tipo *',
                    labelStyle: TextStyle(color: Colors.grey),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Colors.grey),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: Color(0xFF4ECDC4)),
                    ),
                  ),
                  items: _tipos.map((tipo) {
                    return DropdownMenuItem(value: tipo, child: Text(tipo));
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _tipoSelecionado = value!;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // Cores
                const Text(
                  'Cor',
                  style: TextStyle(color: Colors.grey, fontSize: 16),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: _cores.map((cor) {
                    final isSelected = _corSelecionada == cor;
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _corSelecionada = cor;
                        });
                      },
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Color(
                            int.parse(cor.replaceFirst('#', '0xFF')),
                          ),
                          shape: BoxShape.circle,
                          border: isSelected
                              ? Border.all(color: Colors.white, width: 3)
                              : null,
                        ),
                        child: isSelected
                            ? const Icon(Icons.check, color: Colors.white)
                            : null,
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancelar', style: TextStyle(color: Colors.grey)),
        ),
        ElevatedButton(
          onPressed: _salvar,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF4ECDC4),
          ),
          child: Text(
            widget.categoria == null ? 'Criar' : 'Salvar',
            style: const TextStyle(color: Colors.white),
          ),
        ),
      ],
    );
  }

  void _salvar() {
    if (_formKey.currentState!.validate()) {
      final categoria = CategoriaCreateUpdateModel(
        nome: _nomeController.text.trim(),
        descricao: _descricaoController.text.trim().isEmpty
            ? null
            : _descricaoController.text.trim(),
        tipo: _tipoSelecionado,
        cor: _corSelecionada,
        icone: _iconeSelecionado,
        ordem: _ordem,
      );

      widget.onSave(categoria);
      Navigator.pop(context);
    }
  }
}
