import 'package:flutter/material.dart';
import '../../../core/utils/navigation_helper.dart';
import '../data/cards_service.dart';
import '../data/models/card_model.dart';
import 'add_card_screen.dart';

/// Tela de listagem de cartões seguindo o padrão das categorias
class CardsListScreen extends StatefulWidget {
  const CardsListScreen({super.key});

  @override
  State<CardsListScreen> createState() => _CardsListScreenState();
}

class _CardsListScreenState extends State<CardsListScreen> {
  final CardsService _cardsService = CardsService();
  final TextEditingController _searchController = TextEditingController();
  
  List<CardModel> _allCards = [];
  List<CardModel> _filteredCards = [];
  bool _isLoading = true;
  String? _error;
  String _selectedFilter = 'TODOS';

  final List<Map<String, dynamic>> _filters = [
    {'label': 'TODOS', 'icon': Icons.credit_card, 'color': Color(0xFF4ECDC4)},
    {'label': 'ATIVOS', 'icon': Icons.check_circle, 'color': Color(0xFF4ECDC4)},
    {'label': 'INATIVOS', 'icon': Icons.cancel, 'color': Colors.red},
    {'label': 'VISA', 'icon': Icons.credit_card, 'color': Color(0xFF1A1F71)},
    {'label': 'MASTERCARD', 'icon': Icons.credit_card, 'color': Color(0xFFEB001B)},
    {'label': 'ELO', 'icon': Icons.credit_card, 'color': Color(0xFFFFCC00)},
  ];

  @override
  void initState() {
    super.initState();
    _loadCards();
    _searchController.addListener(_filterCards);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadCards() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final cards = await _cardsService.getAllCards();
      
      setState(() {
        _allCards = cards;
        _filteredCards = cards;
        _isLoading = false;
      });
      
      _filterCards();
    } catch (e) {
      setState(() {
        _error = 'Erro ao carregar cartões: $e';
        _isLoading = false;
      });
    }
  }

  void _filterCards() {
    String searchTerm = _searchController.text.toLowerCase();
    
    setState(() {
      _filteredCards = _allCards.where((card) {
        // Filtro por busca
        bool matchesSearch = searchTerm.isEmpty ||
            card.nomeCartao.toLowerCase().contains(searchTerm) ||
            card.bandeira.toLowerCase().contains(searchTerm) ||
            (card.apelido?.toLowerCase().contains(searchTerm) ?? false);

        // Filtro por categoria
        bool matchesFilter = true;
        switch (_selectedFilter) {
          case 'ATIVOS':
            matchesFilter = card.ativo;
            break;
          case 'INATIVOS':
            matchesFilter = !card.ativo;
            break;
          case 'VISA':
          case 'MASTERCARD':
          case 'ELO':
            matchesFilter = card.bandeira.toUpperCase() == _selectedFilter;
            break;
          default: // TODOS
            matchesFilter = true;
        }

        return matchesSearch && matchesFilter;
      }).toList();
    });
  }

  Color _getCardColor(String bandeira) {
    switch (bandeira.toUpperCase()) {
      case 'VISA':
        return const Color(0xFF1A1F71);
      case 'MASTERCARD':
        return const Color(0xFFEB001B);
      case 'ELO':
        return const Color(0xFFFFCC00);
      default:
        return const Color(0xFF4ECDC4);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E), // Mesmo fundo das categorias
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => NavigationHelper.safeGoBack(context),
        ),
        title: const Text(
          'Cartões',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddCardScreen(),
                ),
              ).then((_) => _loadCards());
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadCards,
        child: Column(
          children: [
            // Barra de pesquisa
            Container(
              margin: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: const Color(0xFF16213E),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(color: const Color(0xFF4ECDC4).withValues(alpha: 0.3)),
              ),
              child: TextField(
                controller: _searchController,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  hintText: 'Pesquisar cartões...',
                  hintStyle: TextStyle(color: Colors.white54),
                  prefixIcon: Icon(Icons.search, color: Colors.white54),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                ),
              ),
            ),

            // Filtros horizontais
            Container(
              height: 50,
              margin: const EdgeInsets.symmetric(horizontal: 20),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _filters.length,
                itemBuilder: (context, index) {
                  final filter = _filters[index];
                  final isSelected = _selectedFilter == filter['label'];
                  
                  return Container(
                    margin: const EdgeInsets.only(right: 10),
                    child: FilterChip(
                      selected: isSelected,
                      label: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            filter['icon'],
                            size: 16,
                            color: isSelected ? Colors.white : filter['color'],
                          ),
                          const SizedBox(width: 5),
                          Text(
                            filter['label'],
                            style: TextStyle(
                              color: isSelected ? Colors.white : Colors.white70,
                              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                      onSelected: (selected) {
                        setState(() {
                          _selectedFilter = filter['label'];
                        });
                        _filterCards();
                      },
                      backgroundColor: const Color(0xFF16213E),
                      selectedColor: filter['color'],
                      side: BorderSide(
                        color: isSelected ? filter['color'] : filter['color'].withValues(alpha: 0.3),
                      ),
                    ),
                  );
                },
              ),
            ),

            const SizedBox(height: 20),

            // Lista de cartões
            Expanded(
              child: _buildCardsList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardsList() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4ECDC4)),
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(color: Colors.white70),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadCards,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4ECDC4),
              ),
              child: const Text(
                'Tentar Novamente',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      );
    }

    if (_filteredCards.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.credit_card_off,
              size: 64,
              color: Colors.white54,
            ),
            const SizedBox(height: 16),
            Text(
              _allCards.isEmpty 
                ? 'Nenhum cartão cadastrado'
                : 'Nenhum cartão encontrado',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _allCards.isEmpty 
                ? 'Adicione seu primeiro cartão para começar'
                : 'Tente ajustar os filtros de busca',
              style: const TextStyle(
                color: Colors.white54,
                fontSize: 14,
              ),
            ),
            if (_allCards.isEmpty) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const AddCardScreen(),
                    ),
                  ).then((_) => _loadCards());
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4ECDC4),
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
                icon: const Icon(Icons.add, color: Colors.white),
                label: const Text(
                  'Adicionar Cartão',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      itemCount: _filteredCards.length,
      itemBuilder: (context, index) {
        final card = _filteredCards[index];
        final cardColor = _getCardColor(card.bandeira);
        
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: Card(
            color: const Color(0xFF16213E),
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
              side: BorderSide(
                color: cardColor.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: ListTile(
              contentPadding: const EdgeInsets.all(16),
              leading: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: cardColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.credit_card,
                  color: cardColor,
                  size: 24,
                ),
              ),
              title: Text(
                card.apelido?.isNotEmpty == true ? card.apelido! : card.nomeCartao,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 4),
                  Text(
                    '${card.bandeira} •••• ${card.ultimosDigitos}',
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Limite: R\$ ${card.limite.toStringAsFixed(2).replaceAll('.', ',')}',
                    style: const TextStyle(
                      color: Colors.white54,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: card.ativo ? const Color(0xFF4ECDC4) : Colors.red,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      card.ativo ? 'Ativo' : 'Inativo',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert, color: Colors.white54),
                    color: const Color(0xFF16213E),
                    onSelected: (value) => _handleMenuAction(value, card),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit, color: Colors.white54, size: 20),
                            SizedBox(width: 8),
                            Text('Editar', style: TextStyle(color: Colors.white)),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: card.ativo ? 'deactivate' : 'activate',
                        child: Row(
                          children: [
                            Icon(
                              card.ativo ? Icons.block : Icons.check_circle,
                              color: card.ativo ? Colors.red : Colors.green,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              card.ativo ? 'Desativar' : 'Ativar',
                              style: const TextStyle(color: Colors.white),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              onTap: () {
                // Navegar para detalhes do cartão
              },
            ),
          ),
        );
      },
    );
  }

  void _handleMenuAction(String action, CardModel card) {
    switch (action) {
      case 'edit':
        // Navegar para edição
        break;
      case 'activate':
      case 'deactivate':
        _toggleCardStatus(card);
        break;
    }
  }

  void _toggleCardStatus(CardModel card) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213E),
        title: Text(
          card.ativo ? 'Desativar Cartão' : 'Ativar Cartão',
          style: const TextStyle(color: Colors.white),
        ),
        content: Text(
          card.ativo 
            ? 'Tem certeza que deseja desativar este cartão?'
            : 'Tem certeza que deseja ativar este cartão?',
          style: const TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Cancelar',
              style: TextStyle(color: Colors.white54),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              // Implementar toggle do status
              await _loadCards();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: card.ativo ? Colors.red : const Color(0xFF4ECDC4),
            ),
            child: Text(
              card.ativo ? 'Desativar' : 'Ativar',
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
