import '../../../core/services/api_service.dart';
import '../../../core/services/logger_service.dart';
import '../models/meta_filter_model.dart';
import '../models/meta_response_models.dart';
import '../models/meta_request_models.dart';
import '../models/goal_model.dart';

/// Serviço para gerenciar metas financeiras integrado com a API refatorada
class GoalsService {
  static const String _baseUrl = 'meta';

  /// Busca todas as metas com filtros (endpoint GetAll)
  static Future<MetaPaginatedResponseModel> getAllMetas({
    MetaFilterModel? filtros,
  }) async {
    try {
      LoggerService.info('Buscando metas financeiras com filtros...');

      // Construir query parameters
      final queryParams = filtros?.toQueryParams() ?? {};

      final response = await ApiService.get(_baseUrl, queryParams: queryParams);
      LoggerService.info('Metas financeiras obtidas da API com sucesso');

      if (response is Map<String, dynamic>) {
        return MetaPaginatedResponseModel.fromJson(response);
      } else if (response is Map<String, dynamic> &&
          response.containsKey('data')) {
        return MetaPaginatedResponseModel.fromJson(
          response['data'] as Map<String, dynamic>,
        );
      }

      LoggerService.warning('Resposta da API não contém dados válidos');
      return MetaPaginatedResponseModel.fromJson({});
    } catch (e) {
      LoggerService.failure('Erro ao buscar metas financeiras: $e');
      rethrow;
    }
  }

  /// Busca uma meta específica por ID (endpoint GetById)
  static Future<MetaViewModel?> getMetaById(String id) async {
    try {
      LoggerService.info('Buscando meta por ID: $id');

      final response = await ApiService.get('$_baseUrl/$id');
      LoggerService.info('Meta obtida da API com sucesso');

      if (response is Map<String, dynamic>) {
        if (response.containsKey('data')) {
          return MetaViewModel.fromJson(
            response['data'] as Map<String, dynamic>,
          );
        } else {
          return MetaViewModel.fromJson(response);
        }
      }

      LoggerService.warning('Resposta da API não contém dados válidos');
      return null;
    } catch (e) {
      LoggerService.failure('Erro ao buscar meta: $e');
      rethrow;
    }
  }

  /// Cria ou atualiza uma meta (endpoint CreateOrUpdate)
  static Future<MetaViewModel> createOrUpdateMeta(
    MetaCreateUpdateRequestModel request,
  ) async {
    try {
      final isUpdate = request.isUpdate;
      LoggerService.info(
        isUpdate
            ? 'Atualizando meta: ${request.id}'
            : 'Criando nova meta: ${request.nome}',
      );

      final response = isUpdate
          ? await ApiService.put(_baseUrl, request.toJson())
          : await ApiService.post(_baseUrl, request.toJson());

      LoggerService.info(
        isUpdate
            ? 'Meta atualizada com sucesso na API'
            : 'Meta criada com sucesso na API',
      );

      if (response is Map<String, dynamic>) {
        if (response.containsKey('data')) {
          return MetaViewModel.fromJson(
            response['data'] as Map<String, dynamic>,
          );
        } else {
          return MetaViewModel.fromJson(response);
        }
      }

      throw Exception('Resposta da API não contém dados válidos');
    } catch (e) {
      LoggerService.failure(
        'Erro ao ${request.isUpdate ? 'atualizar' : 'criar'} meta: $e',
      );
      rethrow;
    }
  }

  /// Remove uma meta (endpoint Delete - soft delete)
  static Future<bool> deleteMeta(String id) async {
    try {
      LoggerService.info('Removendo meta: $id');

      await ApiService.delete('$_baseUrl/$id');
      LoggerService.info('Meta removida com sucesso da API');
      return true;
    } catch (e) {
      LoggerService.failure('Erro ao remover meta: $e');
      rethrow;
    }
  }

  /// Atualiza progresso ou marca meta como concluída (endpoint CompleteOrUpdate)
  static Future<MetaViewModel> completeOrUpdateMeta(
    String id,
    MetaCompleteUpdateRequestModel request,
  ) async {
    try {
      LoggerService.info('Atualizando progresso da meta: $id');

      final response = await ApiService.put(
        '$_baseUrl/$id/complete',
        request.toJson(),
      );
      LoggerService.info('Progresso da meta atualizado com sucesso na API');

      if (response is Map<String, dynamic>) {
        if (response.containsKey('data')) {
          return MetaViewModel.fromJson(
            response['data'] as Map<String, dynamic>,
          );
        } else {
          return MetaViewModel.fromJson(response);
        }
      }

      throw Exception('Resposta da API não contém dados válidos');
    } catch (e) {
      LoggerService.failure('Erro ao atualizar progresso da meta: $e');
      rethrow;
    }
  }

  /// Busca opções de filtro disponíveis (endpoint GetFilterOptions)
  static Future<Map<String, dynamic>> getFilterOptions() async {
    try {
      LoggerService.info('Buscando opções de filtro...');

      final response = await ApiService.get('$_baseUrl/filter-options');
      LoggerService.info('Opções de filtro obtidas da API com sucesso');

      if (response is Map<String, dynamic>) {
        if (response.containsKey('data')) {
          return response['data'] as Map<String, dynamic>;
        } else {
          return response;
        }
      }

      LoggerService.warning('Resposta da API não contém dados válidos');
      return {};
    } catch (e) {
      LoggerService.failure('Erro ao buscar opções de filtro: $e');
      rethrow;
    }
  }

  /// Método de compatibilidade para buscar todas as metas (para dashboard)
  static Future<List<GoalModel>> getAllGoals() async {
    try {
      final filtros = MetaFilterModel(apenasAtivas: true);

      final response = await getAllMetas(filtros: filtros);

      // Converter MetaViewModel para GoalModel para compatibilidade com dashboard
      return response.metas.map((meta) => _convertMetaToGoal(meta)).toList();
    } catch (e) {
      LoggerService.failure('Erro ao buscar metas para dashboard: $e');
      return [];
    }
  }

  /// Converte MetaViewModel para GoalModel (compatibilidade com dashboard)
  static GoalModel _convertMetaToGoal(MetaViewModel meta) {
    return GoalModel(
      id: meta.id ?? '',
      titulo: meta.nome,
      descricao: meta.descricao ?? '',
      valorMeta: meta.valorAlvo,
      valorAtual: meta.progressoAtual,
      dataInicio: meta.dataAbertura ?? meta.dtaCadastro,
      dataFim:
          meta.dataConclusao ?? DateTime.now().add(const Duration(days: 365)),
      categoria: meta.categoriasAssociadas.isNotEmpty
          ? meta.categoriasAssociadas.first
          : '',
      cor: '#4ECDC4', // Cor padrão
      ativa: meta.flgAtivo,
    );
  }
}
