import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/utils/navigation_helper.dart';
import '../../accounts/data/accounts_service.dart';
import '../../accounts/data/models/account_model.dart';
import '../../categories/data/categories_service.dart';
import '../../categories/models/category_model.dart';

class AddTransactionScreen extends StatefulWidget {
  final String? preSelectedAccountId;

  const AddTransactionScreen({super.key, this.preSelectedAccountId});

  @override
  State<AddTransactionScreen> createState() => _AddTransactionScreenState();
}

class _AddTransactionScreenState extends State<AddTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _amountController = TextEditingController();

  String _transactionType = 'DESPESA';
  String? _selectedAccountId;
  String? _selectedCategoryId;
  DateTime _selectedDate = DateTime.now();

  List<AccountModel> _accounts = [];
  List<CategoryModel> _categories = [];
  bool _isLoadingAccounts = true;
  bool _isLoadingCategories = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _amountController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    await Future.wait([_loadAccounts(), _loadCategories()]);
  }

  Future<void> _loadAccounts() async {
    try {
      setState(() => _isLoadingAccounts = true);
      final result = await AccountsService.getAccounts();
      setState(() {
        _accounts = result.accounts;
        _isLoadingAccounts = false;
      });
    } catch (e) {
      setState(() => _isLoadingAccounts = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erro ao carregar contas'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadCategories() async {
    try {
      setState(() => _isLoadingCategories = true);
      final categories = await CategoriesService.getCategoriesByType(
        _transactionType,
      );
      setState(() {
        _categories = categories;
        _isLoadingCategories = false;
      });
    } catch (e) {
      setState(() => _isLoadingCategories = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erro ao carregar categorias'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF1A1A2E),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => NavigationHelper.safeGoBack(context),
        ),
        title: const Text(
          'Nova Transação',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header informativo
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: const Color(0xFF16213E),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(
                    color: const Color(0xFF4ECDC4).withValues(alpha: 0.3),
                  ),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.add_circle, color: Color(0xFF4ECDC4), size: 24),
                    SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Registre uma nova transação para controlar suas finanças',
                        style: TextStyle(color: Colors.white70),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 30),

              // Tipo de transação
              _buildTransactionTypeSelector(),
              const SizedBox(height: 20),

              // Descrição
              _buildTextField(
                controller: _descriptionController,
                label: 'Descrição',
                hint: 'Ex: Supermercado, Salário...',
                icon: Icons.description,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Descrição é obrigatória';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),

              // Valor
              _buildTextField(
                controller: _amountController,
                label: 'Valor',
                hint: '0,00',
                icon: Icons.attach_money,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.,]')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Valor é obrigatório';
                  }
                  final amount = double.tryParse(value.replaceAll(',', '.'));
                  if (amount == null || amount <= 0) {
                    return 'Valor deve ser maior que zero';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),

              // Conta
              _buildAccountDropdown(),
              const SizedBox(height: 20),

              // Categoria
              _buildCategoryDropdown(),
              const SizedBox(height: 20),

              // Data
              _buildDateField(),
              const SizedBox(height: 30),

              // Preview da transação
              _buildTransactionPreview(),
              const SizedBox(height: 30),

              // Botões de ação
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Colors.white54),
                        padding: const EdgeInsets.symmetric(vertical: 15),
                      ),
                      child: const Text(
                        'Cancelar',
                        style: TextStyle(color: Colors.white54),
                      ),
                    ),
                  ),
                  const SizedBox(width: 15),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _saveTransaction,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                        padding: const EdgeInsets.symmetric(vertical: 15),
                      ),
                      child: const Text(
                        'Salvar Alterações',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTransactionTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Tipo de Transação',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => setState(() => _transactionType = 'DESPESA'),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _transactionType == 'DESPESA'
                        ? Colors.red
                        : const Color(0xFF16213E),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _transactionType == 'DESPESA'
                          ? Colors.red
                          : Colors.white24,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.remove_circle,
                        color: _transactionType == 'DESPESA'
                            ? Colors.white
                            : Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Despesa',
                        style: TextStyle(
                          color: _transactionType == 'DESPESA'
                              ? Colors.white
                              : Colors.white70,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: InkWell(
                onTap: () => setState(() => _transactionType = 'RECEITA'),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: _transactionType == 'RECEITA'
                        ? Colors.green
                        : const Color(0xFF16213E),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: _transactionType == 'RECEITA'
                          ? Colors.green
                          : Colors.white24,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add_circle,
                        color: _transactionType == 'RECEITA'
                            ? Colors.white
                            : Colors.green,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Receita',
                        style: TextStyle(
                          color: _transactionType == 'RECEITA'
                              ? Colors.white
                              : Colors.white70,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          validator: validator,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Colors.white54),
            prefixIcon: Icon(icon, color: Colors.white54),
            filled: true,
            fillColor: const Color(0xFF16213E),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.orange),
            ),
          ),
          onChanged: (value) => setState(() {}),
        ),
      ],
    );
  }

  Widget _buildAccountDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Conta',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedAccountId,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            prefixIcon: const Icon(
              Icons.account_balance,
              color: Colors.white54,
            ),
            filled: true,
            fillColor: const Color(0xFF16213E),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.orange),
            ),
          ),
          dropdownColor: const Color(0xFF16213E),
          hint: const Text(
            'Selecione uma conta',
            style: TextStyle(color: Colors.white54),
          ),
          items: _isLoadingAccounts
              ? []
              : _accounts.map((account) {
                  return DropdownMenuItem<String>(
                    value: account.id,
                    child: Text(
                      account.nomeBanco,
                      style: const TextStyle(color: Colors.white),
                    ),
                  );
                }).toList(),
          onChanged: (String? newValue) {
            setState(() {
              _selectedAccountId = newValue;
            });
          },
          validator: (value) {
            if (value == null) {
              return 'Selecione uma conta';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildCategoryDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Categoria',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedCategoryId,
          style: const TextStyle(color: Colors.white),
          decoration: InputDecoration(
            prefixIcon: const Icon(Icons.category, color: Colors.white54),
            filled: true,
            fillColor: const Color(0xFF16213E),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Colors.orange),
            ),
          ),
          dropdownColor: const Color(0xFF16213E),
          hint: const Text(
            'Selecione uma categoria',
            style: TextStyle(color: Colors.white54),
          ),
          items: _isLoadingCategories
              ? []
              : _categories.map((category) {
                  return DropdownMenuItem<String>(
                    value: category.id,
                    child: Text(
                      category.nome,
                      style: const TextStyle(color: Colors.white),
                    ),
                  );
                }).toList(),
          onChanged: (String? newValue) {
            setState(() {
              _selectedCategoryId = newValue;
            });
          },
          validator: (value) {
            if (value == null) {
              return 'Selecione uma categoria';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildDateField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Data da Transação',
          style: TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        InkWell(
          onTap: _selectDate,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF16213E),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                const Icon(Icons.calendar_today, color: Colors.white54),
                const SizedBox(width: 12),
                Text(
                  _formatDate(_selectedDate),
                  style: const TextStyle(color: Colors.white),
                ),
                const Spacer(),
                const Icon(Icons.arrow_drop_down, color: Colors.white54),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionPreview() {
    final amount =
        double.tryParse(_amountController.text.replaceAll(',', '.')) ?? 0.0;
    final description = _descriptionController.text.isEmpty
        ? 'Descrição da transação'
        : _descriptionController.text;
    final accountName = _selectedAccountId != null
        ? _accounts
                  .where((acc) => acc.id == _selectedAccountId)
                  .firstOrNull
                  ?.nomeBanco ??
              'Conta'
        : 'Selecione uma conta';
    final categoryName = _selectedCategoryId != null
        ? _categories
                  .where((cat) => cat.id == _selectedCategoryId)
                  .firstOrNull
                  ?.nome ??
              'Categoria'
        : 'Selecione uma categoria';

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF16213E),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _transactionType == 'RECEITA'
                    ? Icons.add_circle
                    : Icons.remove_circle,
                color: _transactionType == 'RECEITA'
                    ? Colors.green
                    : Colors.red,
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text(
                'Preview da Transação',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Text(
            description,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${_transactionType == 'RECEITA' ? '+' : '-'}R\$ ${amount.toStringAsFixed(2)}',
            style: TextStyle(
              color: _transactionType == 'RECEITA' ? Colors.green : Colors.red,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Conta',
                      style: TextStyle(color: Colors.white54, fontSize: 12),
                    ),
                    Text(
                      accountName,
                      style: const TextStyle(color: Colors.white70),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Categoria',
                      style: TextStyle(color: Colors.white54, fontSize: 12),
                    ),
                    Text(
                      categoryName,
                      style: const TextStyle(color: Colors.white70),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Data: ${_formatDate(_selectedDate)}',
            style: const TextStyle(color: Colors.white54, fontSize: 12),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 30)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.dark(
              primary: Colors.orange,
              surface: Color(0xFF16213E),
            ),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      setState(() => _selectedDate = date);
    }
  }

  void _saveTransaction() async {
    if (_formKey.currentState!.validate()) {
      if (_selectedAccountId == null || _selectedCategoryId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Selecione uma conta e uma categoria'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      try {
        final newTransaction = {
          'descricao': _descriptionController.text,
          'valor': _transactionType == 'DESPESA'
              ? -(double.tryParse(
                      _amountController.text.replaceAll(',', '.'),
                    ) ??
                    0.0)
              : (double.tryParse(_amountController.text.replaceAll(',', '.')) ??
                    0.0),
          'isExpense': _transactionType == 'DESPESA',
          'contaId': _selectedAccountId,
          'categoriaId': _selectedCategoryId,
          'data': _selectedDate,
          'dataCriacao': DateTime.now(),
        };

        // Aqui você pode chamar o serviço para salvar a transação
        // await TransactionsService.createTransaction(newTransaction);

        NavigationHelper.safeGoBack(context);

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Transação criada com sucesso!'),
            backgroundColor: Colors.green,
          ),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erro ao salvar transação: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
