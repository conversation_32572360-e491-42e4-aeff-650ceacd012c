namespace Shared.ViewModels.Client
{
    /// <summary>
    /// ViewModel de resposta para metas (sem paginação)
    /// </summary>
    public class MetaPaginatedResponseViewModel
    {
        /// <summary>
        /// Lista de todas as metas encontradas
        /// </summary>
        public List<MetaViewModel> Metas { get; set; } = new List<MetaViewModel>();

        /// <summary>
        /// Filtros aplicados na consulta
        /// </summary>
        public MetaFilterViewModel? FiltrosAplicados { get; set; }

        /// <summary>
        /// Estatísticas dos resultados filtrados
        /// </summary>
        public EstatisticasResultado Estatisticas { get; set; } = new EstatisticasResultado();
    }



    /// <summary>
    /// Estatísticas dos resultados filtrados
    /// </summary>
    public class EstatisticasResultado
    {
        /// <summary>
        /// Total de metas encontradas com os filtros aplicados
        /// </summary>
        public int TotalEncontradas { get; set; }

        /// <summary>
        /// Quantidade por status
        /// </summary>
        public Dictionary<string, int> QuantidadePorStatus { get; set; } = new Dictionary<string, int>();

        /// <summary>
        /// Valor total das metas encontradas
        /// </summary>
        public decimal ValorTotalMetas { get; set; }

        /// <summary>
        /// Valor total já alcançado das metas encontradas
        /// </summary>
        public decimal ValorTotalAlcancado { get; set; }

        /// <summary>
        /// Percentual médio de progresso das metas encontradas
        /// </summary>
        public decimal ProgressoMedio { get; set; }

        /// <summary>
        /// Quantidade de metas por categoria
        /// </summary>
        public Dictionary<string, int> QuantidadePorCategoria { get; set; } = new Dictionary<string, int>();
    }
}
