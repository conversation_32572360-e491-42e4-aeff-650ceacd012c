using Shared.Enums;

namespace Shared.ViewModels.Client
{
    /// <summary>
    /// ViewModel que retorna as opções disponíveis para filtrar metas
    /// </summary>
    public class MetaFilterOptionsViewModel
    {
        /// <summary>
        /// Lista de status disponíveis para filtro
        /// </summary>
        public List<MetaStatusOption> StatusDisponiveis { get; set; } = new List<MetaStatusOption>();

        /// <summary>
        /// Lista de categorias disponíveis para filtro
        /// </summary>
        public List<CategoriaOption> CategoriasDisponiveis { get; set; } = new List<CategoriaOption>();

        /// <summary>
        /// Opções de ordenação disponíveis
        /// </summary>
        public List<OrdenacaoOption> OpcoesOrdenacao { get; set; } = new List<OrdenacaoOption>();

        /// <summary>
        /// Faixa de valores das metas (mínimo e máximo)
        /// </summary>
        public FaixaValores FaixaValoresAlvo { get; set; } = new FaixaValores();

        /// <summary>
        /// Faixa de datas das metas (mais antiga e mais recente)
        /// </summary>
        public FaixaDatas FaixaDatasCriacao { get; set; } = new FaixaDatas();

        /// <summary>
        /// Faixa de datas de vencimento das metas
        /// </summary>
        public FaixaDatas FaixaDatasVencimento { get; set; } = new FaixaDatas();

        /// <summary>
        /// Estatísticas gerais das metas
        /// </summary>
        public EstatisticasMetas Estatisticas { get; set; } = new EstatisticasMetas();
    }

    /// <summary>
    /// Opção de status para filtro
    /// </summary>
    public class MetaStatusOption
    {
        /// <summary>
        /// Valor do enum do status
        /// </summary>
        public MetaStatus Valor { get; set; }

        /// <summary>
        /// Descrição do status
        /// </summary>
        public string Descricao { get; set; } = "";

        /// <summary>
        /// Quantidade de metas com este status
        /// </summary>
        public int Quantidade { get; set; }
    }

    /// <summary>
    /// Opção de categoria para filtro
    /// </summary>
    public class CategoriaOption
    {
        /// <summary>
        /// ID da categoria
        /// </summary>
        public string Id { get; set; } = "";

        /// <summary>
        /// Nome da categoria
        /// </summary>
        public string Nome { get; set; } = "";

        /// <summary>
        /// Cor da categoria
        /// </summary>
        public string Cor { get; set; } = "";

        /// <summary>
        /// Ícone da categoria
        /// </summary>
        public string Icone { get; set; } = "";

        /// <summary>
        /// Quantidade de metas associadas a esta categoria
        /// </summary>
        public int QuantidadeMetas { get; set; }
    }

    /// <summary>
    /// Opção de ordenação
    /// </summary>
    public class OrdenacaoOption
    {
        /// <summary>
        /// Valor do enum de ordenação
        /// </summary>
        public MetaOrdenacao Valor { get; set; }

        /// <summary>
        /// Descrição da ordenação
        /// </summary>
        public string Descricao { get; set; } = "";

        /// <summary>
        /// Nome do campo para ordenação
        /// </summary>
        public string Campo { get; set; } = "";
    }

    /// <summary>
    /// Faixa de valores
    /// </summary>
    public class FaixaValores
    {
        /// <summary>
        /// Valor mínimo
        /// </summary>
        public decimal Minimo { get; set; }

        /// <summary>
        /// Valor máximo
        /// </summary>
        public decimal Maximo { get; set; }

        /// <summary>
        /// Valor médio
        /// </summary>
        public decimal Medio { get; set; }
    }

    /// <summary>
    /// Faixa de datas
    /// </summary>
    public class FaixaDatas
    {
        /// <summary>
        /// Data mais antiga
        /// </summary>
        public DateTime? MaisAntiga { get; set; }

        /// <summary>
        /// Data mais recente
        /// </summary>
        public DateTime? MaisRecente { get; set; }
    }

    /// <summary>
    /// Estatísticas gerais das metas
    /// </summary>
    public class EstatisticasMetas
    {
        /// <summary>
        /// Total de metas
        /// </summary>
        public int TotalMetas { get; set; }

        /// <summary>
        /// Total de metas ativas
        /// </summary>
        public int MetasAtivas { get; set; }

        /// <summary>
        /// Total de metas concluídas
        /// </summary>
        public int MetasConcluidas { get; set; }

        /// <summary>
        /// Total de metas vencidas
        /// </summary>
        public int MetasVencidas { get; set; }

        /// <summary>
        /// Percentual médio de progresso de todas as metas
        /// </summary>
        public decimal ProgressoMedio { get; set; }

        /// <summary>
        /// Valor total de todas as metas
        /// </summary>
        public decimal ValorTotalMetas { get; set; }

        /// <summary>
        /// Valor total já alcançado
        /// </summary>
        public decimal ValorTotalAlcancado { get; set; }
    }
}
