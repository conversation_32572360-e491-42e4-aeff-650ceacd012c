/// Modelo para criação/atualização de metas
class MetaCreateUpdateRequestModel {
  final String? id;
  final String nome;
  final String? descricao;
  final double valorAlvo;
  final double? progressoAtual;
  final DateTime? dataAbertura;
  final DateTime? dataConclusao;
  final bool? flgMensal;
  final List<String>? categoriasIds;

  const MetaCreateUpdateRequestModel({
    this.id,
    required this.nome,
    this.descricao,
    required this.valorAlvo,
    this.progressoAtual,
    this.dataAbertura,
    this.dataConclusao,
    this.flgMensal,
    this.categoriasIds,
  });

  /// Converte para JSON para envio à API
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{
      'nome': nome,
      'valorAlvo': valorAlvo,
    };

    if (id != null && id!.isNotEmpty) {
      json['id'] = id;
    }
    if (descricao != null && descricao!.isNotEmpty) {
      json['descricao'] = descricao;
    }
    if (progressoAtual != null) {
      json['progressoAtual'] = progressoAtual;
    }
    if (dataAbertura != null) {
      json['dataAbertura'] = dataAbertura!.toIso8601String();
    }
    if (dataConclusao != null) {
      json['dataConclusao'] = dataConclusao!.toIso8601String();
    }
    if (flgMensal != null) {
      json['flgMensal'] = flgMensal;
    }
    if (categoriasIds != null && categoriasIds!.isNotEmpty) {
      json['categoriasIds'] = categoriasIds;
    }

    return json;
  }

  /// Cria uma instância a partir de JSON
  factory MetaCreateUpdateRequestModel.fromJson(Map<String, dynamic> json) {
    return MetaCreateUpdateRequestModel(
      id: json['id']?.toString(),
      nome: json['nome']?.toString() ?? '',
      descricao: json['descricao']?.toString(),
      valorAlvo: (json['valorAlvo'] as num?)?.toDouble() ?? 0.0,
      progressoAtual: (json['progressoAtual'] as num?)?.toDouble(),
      dataAbertura: json['dataAbertura'] != null 
          ? DateTime.parse(json['dataAbertura'].toString())
          : null,
      dataConclusao: json['dataConclusao'] != null 
          ? DateTime.parse(json['dataConclusao'].toString())
          : null,
      flgMensal: json['flgMensal'] as bool?,
      categoriasIds: json['categoriasIds'] != null 
          ? List<String>.from(json['categoriasIds'] as List)
          : null,
    );
  }

  /// Cria uma cópia com campos modificados
  MetaCreateUpdateRequestModel copyWith({
    String? id,
    String? nome,
    String? descricao,
    double? valorAlvo,
    double? progressoAtual,
    DateTime? dataAbertura,
    DateTime? dataConclusao,
    bool? flgMensal,
    List<String>? categoriasIds,
  }) {
    return MetaCreateUpdateRequestModel(
      id: id ?? this.id,
      nome: nome ?? this.nome,
      descricao: descricao ?? this.descricao,
      valorAlvo: valorAlvo ?? this.valorAlvo,
      progressoAtual: progressoAtual ?? this.progressoAtual,
      dataAbertura: dataAbertura ?? this.dataAbertura,
      dataConclusao: dataConclusao ?? this.dataConclusao,
      flgMensal: flgMensal ?? this.flgMensal,
      categoriasIds: categoriasIds ?? this.categoriasIds,
    );
  }

  /// Verifica se é uma operação de criação (sem ID)
  bool get isCreate => id == null || id!.isEmpty;

  /// Verifica se é uma operação de atualização (com ID)
  bool get isUpdate => !isCreate;

  @override
  String toString() {
    return 'MetaCreateUpdateRequestModel(id: $id, nome: $nome, valorAlvo: $valorAlvo)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MetaCreateUpdateRequestModel &&
        other.id == id &&
        other.nome == nome &&
        other.valorAlvo == valorAlvo;
  }

  @override
  int get hashCode {
    return Object.hash(id, nome, valorAlvo);
  }
}

/// Modelo para atualização de progresso/conclusão de meta
class MetaCompleteUpdateRequestModel {
  final double? novoProgresso;
  final bool? marcarComoConcluida;
  final String? observacoes;

  const MetaCompleteUpdateRequestModel({
    this.novoProgresso,
    this.marcarComoConcluida,
    this.observacoes,
  });

  /// Converte para JSON para envio à API
  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};

    if (novoProgresso != null) {
      json['novoProgresso'] = novoProgresso;
    }
    if (marcarComoConcluida != null) {
      json['marcarComoConcluida'] = marcarComoConcluida;
    }
    if (observacoes != null && observacoes!.isNotEmpty) {
      json['observacoes'] = observacoes;
    }

    return json;
  }

  /// Cria uma instância a partir de JSON
  factory MetaCompleteUpdateRequestModel.fromJson(Map<String, dynamic> json) {
    return MetaCompleteUpdateRequestModel(
      novoProgresso: (json['novoProgresso'] as num?)?.toDouble(),
      marcarComoConcluida: json['marcarComoConcluida'] as bool?,
      observacoes: json['observacoes']?.toString(),
    );
  }

  /// Cria uma cópia com campos modificados
  MetaCompleteUpdateRequestModel copyWith({
    double? novoProgresso,
    bool? marcarComoConcluida,
    String? observacoes,
  }) {
    return MetaCompleteUpdateRequestModel(
      novoProgresso: novoProgresso ?? this.novoProgresso,
      marcarComoConcluida: marcarComoConcluida ?? this.marcarComoConcluida,
      observacoes: observacoes ?? this.observacoes,
    );
  }

  @override
  String toString() {
    return 'MetaCompleteUpdateRequestModel(novoProgresso: $novoProgresso, marcarComoConcluida: $marcarComoConcluida)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MetaCompleteUpdateRequestModel &&
        other.novoProgresso == novoProgresso &&
        other.marcarComoConcluida == marcarComoConcluida &&
        other.observacoes == observacoes;
  }

  @override
  int get hashCode {
    return Object.hash(novoProgresso, marcarComoConcluida, observacoes);
  }
}
